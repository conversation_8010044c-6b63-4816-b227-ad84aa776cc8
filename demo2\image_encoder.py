import torch
import torch.nn as nn
import torch.nn.functional as F


class ImageCNNEncoder(nn.Module):
    def __init__(self, input_channels, feature_dim):
        super().__init__()
        self.conv1 = nn.Conv2d(input_channels, 16, kernel_size=5, stride=2, padding=2)  # (H/2, W/2)
        self.conv2 = nn.Conv2d(16, 32, kernel_size=5, stride=2, padding=2)  # (H/4, W/4)
        self.conv3 = nn.Conv2d(32, 64, kernel_size=3, stride=2, padding=1)  # (H/8, W/8)
        self.flatten = nn.Flatten()
        self.adaptive_pool = nn.AdaptiveAvgPool2d((4, 4))  # Output fixed size 4x4 feature maps

        # Calculate the input features to the fully connected layer
        # After AdaptiveAvgPool2d((4,4)) and conv3 (64 channels)
        fc_input_features = 64 * 4 * 4
        self.fc = nn.Linear(fc_input_features, feature_dim)
        # print(f"ImageCNNEncoder: input_channels={input_channels}, feature_dim={feature_dim}")
        # print(f"ImageCNNEncoder: fc_input_features from adaptive_pool (64*4*4) = {fc_input_features}")

    def forward(self, x):
        # print(f"ImageCNNEncoder input shape: {x.shape}") # B, C, H, W
        x = F.relu(self.conv1(x))
        # print(f"After conv1 shape: {x.shape}")
        x = F.relu(self.conv2(x))
        # print(f"After conv2 shape: {x.shape}")
        x = F.relu(self.conv3(x))
        # print(f"After conv3 shape: {x.shape}")
        x = self.adaptive_pool(x)
        # print(f"After adaptive_pool shape: {x.shape}")
        x = self.flatten(x)
        # print(f"After flatten shape: {x.shape}")
        x = self.fc(x)
        # print(f"After fc (output) shape: {x.shape}")
        return x


class ImageCNNDecoder(nn.Module):
    def __init__(self, feature_dim, output_channels):
        super().__init__()
        self.fc = nn.Linear(feature_dim, 64 * 4 * 4)
        self.deconv1 = nn.ConvTranspose2d(64, 32, kernel_size=3, stride=2, padding=1, output_padding=1)  # 8x8
        self.deconv2 = nn.ConvTranspose2d(32, 16, kernel_size=5, stride=2, padding=2, output_padding=1)  # 16x16
        self.deconv3 = nn.ConvTranspose2d(
            16, output_channels, kernel_size=5, stride=2, padding=2, output_padding=1
        )  # 32x32
        self.deconv4 = nn.ConvTranspose2d(output_channels, output_channels, kernel_size=4, stride=2, padding=1)  # 64x64

    def forward(self, x):
        # x: (batch, feature_dim)
        x = self.fc(x)
        x = x.view(-1, 64, 4, 4)
        x = F.relu(self.deconv1(x))
        x = F.relu(self.deconv2(x))
        x = F.relu(self.deconv3(x))
        x = torch.sigmoid(self.deconv4(x))  # 输出归一化到[0,1]
        return x  # (batch, output_channels, 64, 64)


class Resblock(nn.Module):
    def __init__(self, in_channels):
        super().__init__()
        self.model = nn.Sequential(
            nn.BatchNorm2d(in_channels),
            nn.ReLU(True),
            nn.Conv2d(in_channels, in_channels, 3, 1, 1, bias=False),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(True),
            nn.Conv2d(in_channels, in_channels, 1, bias=False),
        )

    def forward(self, x):
        return x + self.model(x)


class Resblock_down(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.model = nn.Sequential(
            nn.BatchNorm2d(in_channels),
            nn.ReLU(True),
            nn.Conv2d(in_channels, out_channels, 3, 2, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(True),
            nn.Conv2d(out_channels, out_channels, 3, 1, 1, bias=False),
        )
        self.downsample = nn.Sequential(nn.Conv2d(in_channels, out_channels, 1, 2, bias=False))

    def forward(self, x):
        return self.downsample(x) + self.model(x)


class DTJSCC_CIFAR10_Encoder(nn.Module):
    def __init__(self, input_channels, feature_dim):
        super().__init__()
        self.latent_d = feature_dim
        self.prep = nn.Sequential(
            nn.Conv2d(input_channels, feature_dim // 8, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(feature_dim // 8),
            nn.ReLU(),
        )
        self.layer1 = nn.Sequential(
            nn.Conv2d(feature_dim // 8, feature_dim // 4, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(feature_dim // 4),
            nn.ReLU(),
            nn.MaxPool2d(kernel_size=2, stride=2, padding=0, dilation=1, ceil_mode=False),
        )
        self.layer2 = nn.Sequential(
            nn.Conv2d(feature_dim // 4, feature_dim // 2, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(feature_dim // 2),
            nn.ReLU(),
            nn.MaxPool2d(kernel_size=2, stride=2),
        )
        self.layer3 = nn.Sequential(
            nn.Conv2d(feature_dim // 2, feature_dim, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(feature_dim),
            nn.ReLU(),
            # nn.AvgPool2d(kernel_size = 2, stride = 2, padding = 0, ceil_mode = False)
            nn.MaxPool2d(kernel_size=2, stride=2, padding=0, dilation=1, ceil_mode=False),
        )

        self.encoder = nn.Sequential(
            self.prep,  # 64x32x32
            self.layer1,  # 128x16x16
            Resblock(feature_dim // 4),  # 128x16x16
            self.layer2,  # 256x8x8
            self.layer3,  # 512x4x4
            # Resblock(latent_channels),    # 512x4x4
            Resblock(feature_dim),  # 512x4x4
        )

    def forward(self, X):
        en_X = self.encoder(X)
        en_X = en_X.permute(0, 2, 3, 1).contiguous().view(-1, self.latent_d)
        return en_X


class DTJSCC_CIFAR10_Decoder(nn.Module):
    def __init__(self, feature_dim, output_channels):
        super().__init__()
        self.decoder = nn.Sequential(
            Resblock(feature_dim),  # 8x8
            nn.ConvTranspose2d(feature_dim, feature_dim // 2, kernel_size=4, stride=2, padding=1, bias=False),  # 16x16
            nn.BatchNorm2d(feature_dim // 2),
            nn.ReLU(True),
            Resblock(feature_dim // 2),  # 16x16
            nn.ConvTranspose2d(
                feature_dim // 2, feature_dim // 4, kernel_size=4, stride=2, padding=1, bias=False
            ),  # 32x32
            nn.BatchNorm2d(feature_dim // 4),
            nn.ReLU(True),
            Resblock(feature_dim // 4),  # 32x32
            nn.ConvTranspose2d(
                feature_dim // 4, feature_dim // 8, kernel_size=4, stride=2, padding=1, bias=False
            ),  # 64x64
            nn.BatchNorm2d(feature_dim // 8),
            nn.ReLU(True),
            nn.Conv2d(feature_dim // 8, output_channels, kernel_size=3, stride=1, padding=1, bias=False),
            nn.Sigmoid(),  # 输出归一化到[0,1]
        )

    def forward(self, x):
        # x: (B, feature_dim)
        if x.dim() == 2:
            x = x.view(-1, x.size(1), 8, 8)  # 重塑为(B, feature_dim, 8, 8)
        return self.decoder(x)
