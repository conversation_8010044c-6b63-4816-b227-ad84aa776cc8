"""
Test script for the pure PyTorch PPO implementation.
"""

import torch
import gym
import numpy as np

from env_wrapper import CartPoleImageWrapper
from models.actor_critic import ActorCriticNetwork
from ppo_trainer import PPOTrainer
from rollout_buffer import RolloutBuffer


def test_model_creation():
    """Test that we can create the model without errors."""
    print("Testing model creation...")
    
    # Setup environment to get observation shape
    env = gym.make('CartPole-v1', render_mode='rgb_array')
    env = CartPoleImageWrapper(env, height=64, width=64, grayscale=True)
    
    obs_shape = env.observation_space.shape  # (C, H, W)
    action_dim = env.action_space.n
    
    print(f"Observation shape: {obs_shape}")
    print(f"Action dimension: {action_dim}")
    
    # Create model
    model = ActorCriticNetwork(
        obs_shape=obs_shape,
        action_dim=action_dim,
        latent_dim=64,
        num_embeddings=16,
        gumbel_tau=1.0
    )
    
    print(f"Model created successfully!")
    print(f"Total parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    env.close()
    return model, obs_shape, action_dim


def test_forward_pass(model, obs_shape):
    """Test forward pass through the model."""
    print("\nTesting forward pass...")
    
    # Create dummy observation
    batch_size = 4
    obs = torch.randn(batch_size, *obs_shape)
    
    # Forward pass
    logits, values, z_q, dist_probs = model(obs)
    
    print(f"Input shape: {obs.shape}")
    print(f"Logits shape: {logits.shape}")
    print(f"Values shape: {values.shape}")
    print(f"Quantized features shape: {z_q.shape}")
    print(f"Distribution probs shape: {dist_probs.shape}")
    
    # Test action sampling
    action, log_prob, value = model.act(obs[0])
    print(f"Sampled action: {action}")
    print(f"Log probability: {log_prob}")
    print(f"Value estimate: {value}")
    
    # Test reconstruction
    recon = model.reconstruct(obs)
    print(f"Reconstruction shape: {recon.shape}")
    
    print("Forward pass test passed!")


def test_trainer_creation(model):
    """Test trainer creation and loss computation."""
    print("\nTesting trainer creation...")
    
    device = torch.device('cpu')
    trainer = PPOTrainer(
        model=model,
        learning_rate=3e-4,
        device=device
    )
    
    print("Trainer created successfully!")
    
    # Test loss computation with dummy data
    batch_size = 8
    obs_shape = model.obs_shape
    action_dim = model.action_dim
    
    obs = torch.randn(batch_size, *obs_shape)
    actions = torch.randint(0, action_dim, (batch_size,))
    old_log_probs = torch.randn(batch_size)
    advantages = torch.randn(batch_size)
    returns = torch.randn(batch_size)
    
    total_loss, loss_dict = trainer.compute_ppo_loss(
        obs, actions, old_log_probs, advantages, returns
    )
    
    print(f"Total loss: {total_loss.item():.4f}")
    for key, value in loss_dict.items():
        print(f"{key}: {value.item():.4f}")
    
    print("Trainer test passed!")


def test_rollout_buffer(obs_shape):
    """Test rollout buffer functionality."""
    print("\nTesting rollout buffer...")
    
    device = torch.device('cpu')
    buffer = RolloutBuffer(
        buffer_size=10,
        obs_shape=obs_shape,
        device=device
    )
    
    # Add some dummy data
    for i in range(5):
        obs = np.random.randn(*obs_shape)
        buffer.add(
            obs=obs,
            action=i % 2,
            log_prob=np.random.randn(),
            reward=1.0,
            done=False,
            value=np.random.randn()
        )
    
    print(f"Buffer length: {len(buffer)}")
    
    # Test data processing
    obs_tensor, actions, old_log_probs, advantages, returns = buffer.get_data()
    
    print(f"Processed data shapes:")
    print(f"  Observations: {obs_tensor.shape}")
    print(f"  Actions: {actions.shape}")
    print(f"  Old log probs: {old_log_probs.shape}")
    print(f"  Advantages: {advantages.shape}")
    print(f"  Returns: {returns.shape}")
    
    buffer.clear()
    print(f"Buffer length after clear: {len(buffer)}")
    
    print("Rollout buffer test passed!")


def test_environment_interaction():
    """Test interaction with the environment."""
    print("\nTesting environment interaction...")
    
    # Setup environment
    env = gym.make('CartPole-v1', render_mode='rgb_array')
    env = CartPoleImageWrapper(env, height=64, width=64, grayscale=True)
    
    obs_shape = env.observation_space.shape
    action_dim = env.action_space.n
    
    # Create model
    model = ActorCriticNetwork(
        obs_shape=obs_shape,
        action_dim=action_dim,
        latent_dim=64,
        num_embeddings=16
    )
    
    # Test a few steps
    obs = env.reset()
    total_reward = 0
    
    for step in range(10):
        obs_tensor = torch.tensor(obs, dtype=torch.float32).unsqueeze(0)
        action, log_prob, value = model.act(obs_tensor)
        
        next_obs, reward, done, info = env.step(action)
        total_reward += reward
        
        print(f"Step {step}: action={action}, reward={reward}, value={value:.3f}")
        
        if done:
            obs = env.reset()
            print(f"Episode finished. Total reward: {total_reward}")
            total_reward = 0
        else:
            obs = next_obs
    
    env.close()
    print("Environment interaction test passed!")


def main():
    """Run all tests."""
    print("=" * 50)
    print("Testing Pure PyTorch PPO Implementation")
    print("=" * 50)
    
    try:
        # Test model creation
        model, obs_shape, action_dim = test_model_creation()
        
        # Test forward pass
        test_forward_pass(model, obs_shape)
        
        # Test trainer
        test_trainer_creation(model)
        
        # Test rollout buffer
        test_rollout_buffer(obs_shape)
        
        # Test environment interaction
        test_environment_interaction()
        
        print("\n" + "=" * 50)
        print("All tests passed successfully!")
        print("=" * 50)
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
