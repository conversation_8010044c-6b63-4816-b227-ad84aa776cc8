"""
Quick training test for the pure PyTorch PPO implementation.
"""

import torch
import gym
import numpy as np

from env_wrapper import CartPoleImageWrapper
from models.actor_critic import ActorCriticNetwork
from ppo_trainer import PPOTrainer
from rollout_buffer import RolloutBuffer


def quick_training_test():
    """Run a quick training test with minimal steps."""
    print("Starting quick training test...")
    
    # Configuration
    config = {
        'img_height': 64,
        'img_width': 64,
        'use_grayscale': True,
        'latent_dim': 64,
        'num_embeddings': 16,
        'gumbel_tau': 1.0,
        'learning_rate': 3e-4,
        'n_steps': 32,  # Small for quick test
        'batch_size': 16,
        'n_epochs': 2,  # Small for quick test
        'gamma': 0.99,
        'gae_lambda': 0.95,
        'clip_range': 0.2,
        'entropy_coef': 0.01,
        'value_coef': 0.5,
        'recon_coef': 1.0,
        'quant_entropy_coef': 0.001,
        'max_grad_norm': 1.0,
        'total_timesteps': 100,  # Very small for quick test
    }
    
    device = torch.device('cpu')  # Use CPU for quick test
    
    # Setup environment
    env = gym.make('CartPole-v1', render_mode='rgb_array')
    env = CartPoleImageWrapper(
        env, 
        height=config['img_height'], 
        width=config['img_width'], 
        grayscale=config['use_grayscale']
    )
    
    obs_shape = env.observation_space.shape
    action_dim = env.action_space.n
    
    print(f"Environment setup complete. Obs shape: {obs_shape}, Action dim: {action_dim}")
    
    # Create model
    model = ActorCriticNetwork(
        obs_shape=obs_shape,
        action_dim=action_dim,
        latent_dim=config['latent_dim'],
        num_embeddings=config['num_embeddings'],
        gumbel_tau=config['gumbel_tau']
    ).to(device)
    
    print(f"Model created. Parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Create trainer
    trainer = PPOTrainer(
        model=model,
        learning_rate=config['learning_rate'],
        clip_range=config['clip_range'],
        value_coef=config['value_coef'],
        entropy_coef=config['entropy_coef'],
        recon_coef=config['recon_coef'],
        quant_entropy_coef=config['quant_entropy_coef'],
        max_grad_norm=config['max_grad_norm'],
        device=device
    )
    
    # Create rollout buffer
    rollout_buffer = RolloutBuffer(
        buffer_size=config['n_steps'],
        obs_shape=obs_shape,
        device=device
    )
    
    print("Starting training loop...")
    
    # Training loop
    obs = env.reset()
    episode_rewards = []
    current_episode_reward = 0
    timestep = 0
    update_count = 0
    
    while timestep < config['total_timesteps']:
        # Collect rollout data
        for step in range(config['n_steps']):
            # Convert observation to tensor
            obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)
            
            # Get action from policy
            action, log_prob, value = model.act(obs_tensor)
            
            # Take step in environment
            next_obs, reward, done, info = env.step(action)
            
            # Store transition
            rollout_buffer.add(
                obs=obs,
                action=action,
                log_prob=log_prob.item(),
                reward=reward,
                done=done,
                value=value.item()
            )
            
            # Update state
            obs = next_obs
            current_episode_reward += reward
            timestep += 1
            
            # Handle episode end
            if done:
                episode_rewards.append(current_episode_reward)
                print(f"Episode finished. Reward: {current_episode_reward}")
                current_episode_reward = 0
                obs = env.reset()
            
            # Check if we've collected enough data or reached total timesteps
            if rollout_buffer.is_full() or timestep >= config['total_timesteps']:
                break
        
        # Compute next value for bootstrapping
        if not done and timestep < config['total_timesteps']:
            obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)
            _, _, next_value = model.act(obs_tensor)
            next_value = next_value.item()
        else:
            next_value = 0.0
        
        # Perform PPO update
        print(f"Performing update {update_count + 1} with {len(rollout_buffer)} samples...")
        update_stats = trainer.update(
            rollout_buffer=rollout_buffer,
            n_epochs=config['n_epochs'],
            batch_size=config['batch_size'],
            gamma=config['gamma'],
            gae_lambda=config['gae_lambda'],
            next_value=next_value
        )
        
        update_count += 1
        
        # Print update statistics
        avg_reward = np.mean(episode_rewards[-5:]) if episode_rewards else 0
        print(f"Update {update_count} | Timestep {timestep} | Avg Reward: {avg_reward:.2f}")
        print(f"  Policy Loss: {update_stats['policy_loss']:.4f}")
        print(f"  Value Loss: {update_stats['value_loss']:.4f}")
        print(f"  Recon Loss: {update_stats['recon_loss']:.4f}")
        print(f"  Quant Entropy: {update_stats['quant_entropy_loss']:.4f}")
        print(f"  Total Loss: {update_stats['total_loss']:.4f}")
        
        # Clear buffer for next rollout
        rollout_buffer.clear()
        
        if timestep >= config['total_timesteps']:
            break
    
    print(f"\nTraining completed!")
    print(f"Total timesteps: {timestep}")
    print(f"Total updates: {update_count}")
    print(f"Total episodes: {len(episode_rewards)}")
    if episode_rewards:
        print(f"Average reward: {np.mean(episode_rewards):.2f}")
        print(f"Best reward: {max(episode_rewards):.2f}")
    
    env.close()
    
    # Test reconstruction
    print("\nTesting reconstruction...")
    with torch.no_grad():
        test_obs = torch.tensor(env.reset(), dtype=torch.float32, device=device).unsqueeze(0)
        recon = model.reconstruct(test_obs)
        recon_error = torch.mean((recon - test_obs) ** 2).item()
        print(f"Reconstruction MSE: {recon_error:.6f}")
    
    print("Quick training test completed successfully!")


if __name__ == "__main__":
    quick_training_test()
