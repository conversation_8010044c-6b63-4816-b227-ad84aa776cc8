"""
Actor-Critic network with shared encoder for PPO.
Integrates image encoding, quantization, and policy/value heads.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional
from .encoder_decoder import ImageEncoder, ImageDecoder, VectorQuantizer


class ActorCriticNetwork(nn.Module):
    """
    Unified Actor-Critic network with shared image encoder.
    
    Architecture:
    Input Image -> Encoder -> Quantizer -> [Policy Head, Value Head]
                                      -> Decoder (for reconstruction loss)
    """
    
    def __init__(
        self,
        obs_shape: Tuple[int, int, int],  # (C, H, W)
        action_dim: int,
        latent_dim: int = 64,
        num_embeddings: int = 16,
        gumbel_tau: float = 1.0,
        qam_modem = None
    ):
        super().__init__()
        
        self.obs_shape = obs_shape
        self.action_dim = action_dim
        self.latent_dim = latent_dim
        self.num_embeddings = num_embeddings
        self.qam_modem = qam_modem
        
        C, H, W = obs_shape
        
        # Core components
        self.encoder = ImageEncoder(input_channels=C, feature_dim=latent_dim)
        self.quantizer = VectorQuantizer(
            num_embeddings=num_embeddings,
            embedding_dim=latent_dim,
            gumbel_tau=gumbel_tau
        )
        self.decoder = ImageDecoder(feature_dim=latent_dim, output_channels=C)
        
        # Policy and value heads
        self.policy_head = nn.Linear(latent_dim, action_dim)
        self.value_head = nn.Linear(latent_dim, 1)
        
        # Initialize weights
        self._init_weights()
    
    def _init_weights(self):
        """Initialize policy and value head weights"""
        # Policy head - smaller initialization for stable gradients
        nn.init.orthogonal_(self.policy_head.weight, gain=0.01)
        nn.init.constant_(self.policy_head.bias, 0)
        
        # Value head
        nn.init.orthogonal_(self.value_head.weight, gain=1.0)
        nn.init.constant_(self.value_head.bias, 0)
    
    def encode_and_quantize(self, obs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Encode observations and quantize features.
        
        Args:
            obs: Observations of shape (B, C, H, W)
        
        Returns:
            Tuple of (quantized_features, dist_probs)
        """
        # Encode image to latent features
        latent = self.encoder(obs)  # (B, latent_dim)
        
        # Quantize features
        z_q, dist_probs = self.quantizer(latent, mod=self.qam_modem)  # (B, latent_dim), (B, num_embeddings)
        
        return z_q, dist_probs
    
    def forward(self, obs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Full forward pass for training.
        
        Args:
            obs: Observations of shape (B, C, H, W)
        
        Returns:
            Tuple of (logits, values, z_q, dist_probs)
            - logits: Action logits of shape (B, action_dim)
            - values: State values of shape (B, 1)
            - z_q: Quantized features of shape (B, latent_dim)
            - dist_probs: Quantizer distribution of shape (B, num_embeddings)
        """
        # Encode and quantize
        z_q, dist_probs = self.encode_and_quantize(obs)
        
        # Policy and value outputs
        logits = self.policy_head(z_q)  # (B, action_dim)
        values = self.value_head(z_q)   # (B, 1)
        
        return logits, values, z_q, dist_probs
    
    def act(self, obs: torch.Tensor) -> Tuple[int, torch.Tensor, torch.Tensor]:
        """
        Sample action for environment interaction.
        
        Args:
            obs: Single observation of shape (1, C, H, W) or (C, H, W)
        
        Returns:
            Tuple of (action, log_prob, value)
            - action: Sampled action (int)
            - log_prob: Log probability of action
            - value: State value estimate
        """
        if obs.dim() == 3:  # Add batch dimension if needed
            obs = obs.unsqueeze(0)
        
        with torch.no_grad():
            logits, values, _, _ = self.forward(obs)
            
            # Sample action from policy
            action_probs = F.softmax(logits, dim=-1)
            dist = torch.distributions.Categorical(action_probs)
            action = dist.sample()
            log_prob = dist.log_prob(action)
            
            return action.item(), log_prob.squeeze(), values.squeeze()
    
    def evaluate_actions(self, obs: torch.Tensor, actions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Evaluate actions for PPO loss computation.
        
        Args:
            obs: Observations of shape (B, C, H, W)
            actions: Actions of shape (B,)
        
        Returns:
            Tuple of (log_probs, values, entropy)
        """
        logits, values, _, _ = self.forward(obs)
        
        # Compute action probabilities and distribution
        action_probs = F.softmax(logits, dim=-1)
        dist = torch.distributions.Categorical(action_probs)
        
        # Evaluate actions
        log_probs = dist.log_prob(actions)
        entropy = dist.entropy()
        
        return log_probs, values.squeeze(), entropy
    
    def reconstruct(self, obs: torch.Tensor) -> torch.Tensor:
        """
        Reconstruct images for reconstruction loss.
        
        Args:
            obs: Observations of shape (B, C, H, W)
        
        Returns:
            Reconstructed images of shape (B, C, H, W)
        """
        z_q, _ = self.encode_and_quantize(obs)
        return self.decoder(z_q)
