"""
Pure PyTorch PPO trainer with integrated encoder training.
"""

from typing import Any, Dict, Optional, Tuple

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from models.actor_critic import ActorCriticNetwork
from rollout_buffer import MiniBatchSampler, RolloutBuffer
from torch.utils.tensorboard import SummaryWriter


def compute_entropy_loss(dist_probs: torch.Tensor) -> torch.Tensor:
    """
    Compute entropy loss for quantizer distribution.

    Args:
        dist_probs: Probability distribution of shape (B, num_embeddings)

    Returns:
        Entropy loss (scalar)
    """
    # Add small epsilon for numerical stability
    dist_probs = dist_probs + 1e-8
    entropy = -(dist_probs * torch.log(dist_probs)).sum(dim=-1)
    return entropy.mean()


class PPOTrainer:
    """
    PPO trainer with integrated encoder training.
    """

    def __init__(
        self,
        model: ActorCriticNetwork,
        learning_rate: float = 3e-4,
        clip_range: float = 0.2,
        value_coef: float = 0.5,
        entropy_coef: float = 0.01,
        recon_coef: float = 1.0,
        quant_entropy_coef: float = 0.001,
        max_grad_norm: float = 1.0,
        device: torch.device = torch.device("cpu"),
    ):
        """
        Args:
            model: Actor-Critic network
            learning_rate: Learning rate for optimizer
            clip_range: PPO clipping range
            value_coef: Coefficient for value loss
            entropy_coef: Coefficient for entropy loss
            recon_coef: Coefficient for reconstruction loss
            quant_entropy_coef: Coefficient for quantizer entropy loss
            max_grad_norm: Maximum gradient norm for clipping
            device: Device to run training on
        """
        self.model = model
        self.device = device

        # PPO hyperparameters
        self.clip_range = clip_range
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.recon_coef = recon_coef
        self.quant_entropy_coef = quant_entropy_coef
        self.max_grad_norm = max_grad_norm

        # Optimizer
        self.optimizer = optim.Adam(model.parameters(), lr=learning_rate)

        # Training statistics
        self.training_stats = {
            "policy_loss": [],
            "value_loss": [],
            "entropy_loss": [],
            "recon_loss": [],
            "quant_entropy_loss": [],
            "total_loss": [],
            "approx_kl": [],
            "clip_fraction": [],
        }

    def compute_ppo_loss(
        self,
        obs: torch.Tensor,
        actions: torch.Tensor,
        old_log_probs: torch.Tensor,
        advantages: torch.Tensor,
        returns: torch.Tensor,
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Compute PPO loss with integrated encoder losses.

        Args:
            obs: Observations
            actions: Actions taken
            old_log_probs: Log probabilities from old policy
            advantages: Computed advantages
            returns: Computed returns

        Returns:
            Tuple of (total_loss, loss_dict)
        """
        # Forward pass through model
        logits, values, z_q, dist_probs = self.model(obs)

        # Evaluate actions with current policy
        new_log_probs, _, entropy = self.model.evaluate_actions(obs, actions)

        # PPO policy loss
        ratio = torch.exp(new_log_probs - old_log_probs)
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1 - self.clip_range, 1 + self.clip_range) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()

        # Value loss
        value_loss = 0.5 * (values.squeeze() - returns).pow(2).mean()

        # Entropy loss (negative because we want to maximize entropy)
        entropy_loss = -entropy.mean()

        # Reconstruction loss
        recon_imgs = self.model.decoder(z_q)
        recon_loss = ((recon_imgs - obs) ** 2).mean()

        # Quantizer entropy loss (minimize entropy for hard quantization)
        quant_entropy_loss = compute_entropy_loss(dist_probs)

        # Total loss
        total_loss = (
            policy_loss
            + self.value_coef * value_loss
            + self.entropy_coef * entropy_loss
            + self.recon_coef * recon_loss
            + self.quant_entropy_coef * quant_entropy_loss
        )

        # Compute additional statistics
        with torch.no_grad():
            approx_kl = ((old_log_probs - new_log_probs) ** 2).mean()
            clip_fraction = ((ratio - 1.0).abs() > self.clip_range).float().mean()

        loss_dict = {
            "policy_loss": policy_loss,
            "value_loss": value_loss,
            "entropy_loss": entropy_loss,
            "recon_loss": recon_loss,
            "quant_entropy_loss": quant_entropy_loss,
            "total_loss": total_loss,
            "approx_kl": approx_kl,
            "clip_fraction": clip_fraction,
        }

        return total_loss, loss_dict

    def update(
        self,
        rollout_buffer: RolloutBuffer,
        n_epochs: int = 10,
        batch_size: int = 64,
        gamma: float = 0.99,
        gae_lambda: float = 0.95,
        next_value: float = 0.0,
    ) -> Dict[str, float]:
        """
        Perform PPO update.

        Args:
            rollout_buffer: Buffer containing rollout data
            n_epochs: Number of optimization epochs
            batch_size: Mini-batch size
            gamma: Discount factor
            gae_lambda: GAE lambda
            next_value: Value of next state for bootstrapping

        Returns:
            Dictionary of training statistics
        """
        # Get processed data from buffer
        obs, actions, old_log_probs, advantages, returns = rollout_buffer.get_data(
            gamma=gamma, gae_lambda=gae_lambda, next_value=next_value
        )

        data_size = len(obs)
        epoch_stats = {key: [] for key in self.training_stats.keys()}

        # Perform multiple epochs of optimization
        for epoch in range(n_epochs):
            # Create mini-batch sampler
            sampler = MiniBatchSampler(batch_size, data_size)

            for batch_indices in sampler:
                # Get mini-batch data
                batch_obs = obs[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]

                # Compute loss
                total_loss, loss_dict = self.compute_ppo_loss(
                    batch_obs, batch_actions, batch_old_log_probs, batch_advantages, batch_returns
                )

                # Optimization step
                self.optimizer.zero_grad()
                total_loss.backward()
                nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)
                self.optimizer.step()

                # Record statistics
                for key, value in loss_dict.items():
                    epoch_stats[key].append(value.item())

        # Compute average statistics
        avg_stats = {}
        for key, values in epoch_stats.items():
            avg_value = np.mean(values)
            avg_stats[key] = avg_value
            self.training_stats[key].append(avg_value)

        return avg_stats
