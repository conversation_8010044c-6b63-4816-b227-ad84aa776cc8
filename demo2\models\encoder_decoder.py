"""
Encoder and Decoder modules for image processing.
Reuses the existing DTJSCC_CIFAR10_Encoder and DTJSCC_CIFAR10_Decoder.
"""

from typing import Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F


class ImageEncoder(nn.Module):
    """Wrapper for the existing DTJSCC_CIFAR10_Encoder"""

    def __init__(self, input_channels: int, feature_dim: int):
        super().__init__()
        # Import here to avoid circular imports
        import os
        import sys

        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from image_encoder import DTJSCC_CIFAR10_Encoder

        self.encoder = DTJSCC_CIFAR10_Encoder(input_channels=input_channels, feature_dim=feature_dim)
        self.feature_dim = feature_dim

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: Input images of shape (B, C, H, W)
        Returns:
            Encoded features of shape (B, feature_dim)
        """
        return self.encoder(x)


class ImageDecoder(nn.Module):
    """Wrapper for the existing DTJSCC_CIFAR10_Decoder"""

    def __init__(self, feature_dim: int, output_channels: int):
        super().__init__()
        # Import here to avoid circular imports
        import os
        import sys

        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from image_encoder import DTJSCC_CIFAR10_Decoder

        self.decoder = DTJSCC_CIFAR10_Decoder(feature_dim=feature_dim, output_channels=output_channels)
        self.feature_dim = feature_dim
        self.output_channels = output_channels

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: Encoded features of shape (B, feature_dim)
        Returns:
            Reconstructed images of shape (B, C, H, W)
        """
        return self.decoder(x)


class VectorQuantizer(nn.Module):
    """Wrapper for the existing GumbelSoftmaxQuantizer"""

    def __init__(self, num_embeddings: int, embedding_dim: int, gumbel_tau: float = 1.0):
        super().__init__()
        # Import here to avoid circular imports
        import os
        import sys

        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from quantizer import GumbelSoftmaxQuantizer

        self.quantizer = GumbelSoftmaxQuantizer(
            num_embeddings=num_embeddings, embedding_dim=embedding_dim, gumbel_tau=gumbel_tau
        )
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim

    def forward(self, x: torch.Tensor, mod=None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            x: Input features of shape (B, embedding_dim)
            mod: Optional QAM modulator
        Returns:
            Tuple of (quantized_output, dist_probs)
            - quantized_output: shape (B, embedding_dim)
            - dist_probs: shape (B, num_embeddings)
        """
        return self.quantizer.forward(x, mod=mod)
