import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.optim.lr_scheduler as lr_scheduler
from channel import PSK  # Changed from channel to qam
from gym import spaces  # For type hinting observation_space

# Import your custom modules
from image_encoder import DTJSCC_CIFAR10_Decoder, DTJSCC_CIFAR10_Encoder
from quantizer import GumbelSoftmaxQuantizer
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor

# ModulatorDemodulator class is no longer needed as QAM handles this.


class CustomImageQuantizerExtractor(BaseFeaturesExtractor):
    def __init__(
        self,
        observation_space: spaces.Box,
        num_embeddings: int,  # Codebook size for Quantizer
        embedding_dim: int,  # Embedding dim for Quantizer (this is features_dim for SB3)
        gumbel_tau: float = 1.0,
        qam_m_ary: int = None,  # M-ary value for QAM (e.g., 16 for 16-QAM)
        qam_snr_db: float = None,  # PSNR for the QAM channel in dB (None for no noise)
    ):  # PSNR for the QAM channel in dB (None for no noise)
        super().__init__(observation_space, features_dim=embedding_dim)

        self.image_encoder = DTJSCC_CIFAR10_Encoder(
            input_channels=observation_space.shape[0], feature_dim=embedding_dim
        )
        self.image_decoder = DTJSCC_CIFAR10_Decoder(
            feature_dim=embedding_dim,
            output_channels=observation_space.shape[0],
        )

        self.quantizer = GumbelSoftmaxQuantizer(
            num_embeddings=num_embeddings,
            embedding_dim=embedding_dim,
            gumbel_tau=gumbel_tau,
        )

        self.qam_modem = None
        if qam_m_ary is not None and qam_snr_db is not None:
            if num_embeddings > qam_m_ary:
                raise ValueError(
                    f"Number of quantizer embeddings K ({num_embeddings}) cannot exceed QAM M-ary value ({qam_m_ary})."
                )
            self.qam_modem = PSK(M=qam_m_ary, SNR=qam_snr_db)

        self.image_encoder.train()
        self.image_decoder.train()
        self.quantizer.train()

        params = (
            list(self.image_encoder.parameters())
            + list(self.quantizer.parameters())
            + list(self.image_decoder.parameters())
        )
        self.optimizer = optim.AdamW(params, lr=1e-4, weight_decay=1e-4)
        self.scheduler = lr_scheduler.StepLR(self.optimizer, step_size=80, gamma=0.5)

        self.dist = []
        self.observations = []
        self.recon_imgs = []

    def pop_and_clear_dist(self):
        """获取并清空当前存储的dist列表，创建新的列表而不是返回原始列表的引用"""
        data = self.dist  # 创建新的列表
        self.dist = []
        return data

    def pop_and_clear_observations(self):
        """获取并清空当前存储的observations列表，创建新的列表而不是返回原始列表的引用"""
        data = self.observations  # 创建新的列表
        self.observations = []
        return data

    def pop_and_clear_recon_imgs(self):
        """获取并清空当前存储的recon_imgs列表，创建新的列表而不是返回原始列表的引用"""
        data = self.recon_imgs  # 创建新的列表
        self.recon_imgs = []
        return data

    def forward(self, observations: torch.Tensor) -> torch.Tensor:
        encoded_features = self.image_encoder(observations)
        quantized_output, dist = self.quantizer.forward(encoded_features, mod=self.qam_modem)
        # 存储分布信息用于计算熵损失
        self.dist.append(dist)
        # 返回时使用clone()，避免原地操作
        self.observations.append(observations)
        return quantized_output
