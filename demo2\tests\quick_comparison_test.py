"""
快速对比测试 - 比较有无量化器的性能差异。
"""

import os
import sys
import time

import gym
import numpy as np
import torch
import matplotlib.pyplot as plt
from tqdm import tqdm

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from env_wrapper import CartPoleImageWrapper
from networks.actor_critic import ActorCriticNetwork
from networks.baseline_actor_critic import BaselineActorCriticNetwork
from ppo_trainer import PPOTrainer
from baseline_trainer import BaselinePPOTrainer
from rollout_buffer import RolloutBuffer


def run_quick_experiment(model_type="main", total_timesteps=200):
    """运行快速实验"""
    print(f"\n开始 {model_type} 实验...")
    
    # 配置
    config = {
        "img_height": 64,
        "img_width": 64,
        "use_grayscale": True,
        "latent_dim": 64,
        "num_embeddings": 16,
        "gumbel_tau": 1.0,
        "learning_rate": 3e-4,
        "n_steps": 32,
        "batch_size": 16,
        "n_epochs": 2,
        "gamma": 0.99,
        "gae_lambda": 0.95,
        "clip_range": 0.2,
        "entropy_coef": 0.01,
        "value_coef": 0.5,
        "recon_coef": 1.0,
        "quant_entropy_coef": 0.001,
        "max_grad_norm": 1.0,
        "total_timesteps": total_timesteps,
    }
    
    device = torch.device("cpu")
    
    # 设置环境
    env = gym.make("CartPole-v1", render_mode="rgb_array")
    env = CartPoleImageWrapper(
        env, height=config["img_height"], width=config["img_width"], grayscale=config["use_grayscale"]
    )
    
    obs_shape = env.observation_space.shape
    action_dim = env.action_space.n
    
    # 创建模型和训练器
    if model_type == "main":
        model = ActorCriticNetwork(
            obs_shape=obs_shape,
            action_dim=action_dim,
            latent_dim=config["latent_dim"],
            num_embeddings=config["num_embeddings"],
            gumbel_tau=config["gumbel_tau"],
        ).to(device)
        
        trainer = PPOTrainer(
            model=model,
            learning_rate=config["learning_rate"],
            clip_range=config["clip_range"],
            value_coef=config["value_coef"],
            entropy_coef=config["entropy_coef"],
            recon_coef=config["recon_coef"],
            quant_entropy_coef=config["quant_entropy_coef"],
            max_grad_norm=config["max_grad_norm"],
            device=device,
        )
    else:  # baseline
        model = BaselineActorCriticNetwork(
            obs_shape=obs_shape,
            action_dim=action_dim,
            latent_dim=config["latent_dim"],
        ).to(device)
        
        trainer = BaselinePPOTrainer(
            model=model,
            learning_rate=config["learning_rate"],
            clip_range=config["clip_range"],
            value_coef=config["value_coef"],
            entropy_coef=config["entropy_coef"],
            recon_coef=config["recon_coef"],
            max_grad_norm=config["max_grad_norm"],
            device=device,
        )
    
    # 创建轨迹缓冲区
    rollout_buffer = RolloutBuffer(buffer_size=config["n_steps"], obs_shape=obs_shape, device=device)
    
    # 训练循环
    obs = env.reset()
    episode_rewards = []
    current_episode_reward = 0
    timestep = 0
    update_count = 0
    
    # 记录训练统计
    training_stats = {
        'timesteps': [],
        'rewards': [],
        'policy_loss': [],
        'value_loss': [],
        'recon_loss': [],
        'total_loss': []
    }
    
    start_time = time.time()
    
    with tqdm(total=config["total_timesteps"], desc=f"{model_type} 训练", position=0) as pbar:
        while timestep < config["total_timesteps"]:
            # 收集轨迹数据
            for step in range(config["n_steps"]):
                obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)
                action, log_prob, value = model.act(obs_tensor)
                
                next_obs, reward, done, _ = env.step(action)
                
                rollout_buffer.add(
                    obs=obs, action=action, log_prob=log_prob.item(), 
                    reward=reward, done=done, value=value.item()
                )
                
                obs = next_obs
                current_episode_reward += reward
                timestep += 1
                
                pbar.update(1)
                
                if done:
                    episode_rewards.append(current_episode_reward)
                    current_episode_reward = 0
                    obs = env.reset()
                
                if rollout_buffer.is_full() or timestep >= config["total_timesteps"]:
                    break
            
            # PPO 更新
            if not done and timestep < config["total_timesteps"]:
                obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)
                _, _, next_value = model.act(obs_tensor)
                next_value = next_value.item()
            else:
                next_value = 0.0
            
            update_stats = trainer.update(
                rollout_buffer=rollout_buffer,
                n_epochs=config["n_epochs"],
                batch_size=config["batch_size"],
                gamma=config["gamma"],
                gae_lambda=config["gae_lambda"],
                next_value=next_value,
            )
            
            update_count += 1
            rollout_buffer.clear()
            
            # 记录统计信息
            avg_reward = np.mean(episode_rewards[-5:]) if episode_rewards else 0
            training_stats['timesteps'].append(timestep)
            training_stats['rewards'].append(avg_reward)
            training_stats['policy_loss'].append(update_stats['policy_loss'])
            training_stats['value_loss'].append(update_stats['value_loss'])
            training_stats['recon_loss'].append(update_stats['recon_loss'])
            training_stats['total_loss'].append(update_stats['total_loss'])
            
            pbar.set_postfix({
                "回合": len(episode_rewards),
                "平均奖励": f"{avg_reward:.1f}",
                "更新": update_count
            })
            
            if timestep >= config["total_timesteps"]:
                break
    
    end_time = time.time()
    duration = end_time - start_time
    
    env.close()
    
    # 计算最终统计
    final_stats = {
        'model_type': model_type,
        'total_timesteps': timestep,
        'total_episodes': len(episode_rewards),
        'total_updates': update_count,
        'duration': duration,
        'model_params': sum(p.numel() for p in model.parameters()),
        'final_avg_reward': np.mean(episode_rewards[-10:]) if len(episode_rewards) >= 10 else np.mean(episode_rewards) if episode_rewards else 0,
        'max_reward': max(episode_rewards) if episode_rewards else 0,
        'training_stats': training_stats
    }
    
    print(f"{model_type} 实验完成:")
    print(f"  总步数: {final_stats['total_timesteps']}")
    print(f"  总回合: {final_stats['total_episodes']}")
    print(f"  模型参数: {final_stats['model_params']:,}")
    print(f"  最终平均奖励: {final_stats['final_avg_reward']:.2f}")
    print(f"  最大奖励: {final_stats['max_reward']:.2f}")
    print(f"  训练时间: {final_stats['duration']:.2f} 秒")
    
    return final_stats


def plot_comparison(main_stats, baseline_stats):
    """绘制对比图"""
    print("\n绘制对比图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # 奖励曲线
    axes[0, 0].plot(main_stats['training_stats']['timesteps'], 
                   main_stats['training_stats']['rewards'], 
                   label='有量化器和信道', color='blue', alpha=0.8)
    axes[0, 0].plot(baseline_stats['training_stats']['timesteps'], 
                   baseline_stats['training_stats']['rewards'], 
                   label='基线 (无量化器)', color='red', alpha=0.8)
    axes[0, 0].set_xlabel('训练步数')
    axes[0, 0].set_ylabel('平均奖励')
    axes[0, 0].set_title('训练奖励对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 策略损失
    axes[0, 1].plot(main_stats['training_stats']['timesteps'], 
                   main_stats['training_stats']['policy_loss'], 
                   label='有量化器和信道', color='blue', alpha=0.8)
    axes[0, 1].plot(baseline_stats['training_stats']['timesteps'], 
                   baseline_stats['training_stats']['policy_loss'], 
                   label='基线 (无量化器)', color='red', alpha=0.8)
    axes[0, 1].set_xlabel('训练步数')
    axes[0, 1].set_ylabel('策略损失')
    axes[0, 1].set_title('策略损失对比')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 重构损失
    axes[1, 0].plot(main_stats['training_stats']['timesteps'], 
                   main_stats['training_stats']['recon_loss'], 
                   label='有量化器和信道', color='blue', alpha=0.8)
    axes[1, 0].plot(baseline_stats['training_stats']['timesteps'], 
                   baseline_stats['training_stats']['recon_loss'], 
                   label='基线 (无量化器)', color='red', alpha=0.8)
    axes[1, 0].set_xlabel('训练步数')
    axes[1, 0].set_ylabel('重构损失')
    axes[1, 0].set_title('重构损失对比')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 最终性能对比
    methods = ['有量化器\n和信道', '基线\n(无量化器)']
    performance = [main_stats['final_avg_reward'], baseline_stats['final_avg_reward']]
    colors = ['blue', 'red']
    
    bars = axes[1, 1].bar(methods, performance, color=colors, alpha=0.7)
    axes[1, 1].set_ylabel('最终平均奖励')
    axes[1, 1].set_title('最终性能对比')
    axes[1, 1].grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, perf in zip(bars, performance):
        axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                       f'{perf:.1f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('./quick_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("对比图已保存为 quick_comparison.png")


def main():
    """运行快速对比测试"""
    print("=" * 60)
    print("快速对比测试")
    print("=" * 60)
    print("比较有无量化器和信道传输的性能差异")
    
    total_timesteps = 200  # 快速测试用小值
    
    # 运行主实验
    main_stats = run_quick_experiment("main", total_timesteps)
    
    # 运行基线实验
    baseline_stats = run_quick_experiment("baseline", total_timesteps)
    
    # 比较结果
    print("\n" + "=" * 60)
    print("对比结果")
    print("=" * 60)
    
    print(f"模型参数对比:")
    print(f"  主实验 (有量化器): {main_stats['model_params']:,} 参数")
    print(f"  基线实验 (无量化器): {baseline_stats['model_params']:,} 参数")
    param_diff = main_stats['model_params'] - baseline_stats['model_params']
    print(f"  参数差异: {param_diff:,} ({param_diff/baseline_stats['model_params']*100:.1f}%)")
    
    print(f"\n性能对比:")
    print(f"  主实验最终奖励: {main_stats['final_avg_reward']:.2f}")
    print(f"  基线实验最终奖励: {baseline_stats['final_avg_reward']:.2f}")
    reward_diff = main_stats['final_avg_reward'] - baseline_stats['final_avg_reward']
    print(f"  奖励差异: {reward_diff:.2f}")
    
    print(f"\n训练效率对比:")
    print(f"  主实验训练时间: {main_stats['duration']:.2f} 秒")
    print(f"  基线实验训练时间: {baseline_stats['duration']:.2f} 秒")
    time_diff = main_stats['duration'] - baseline_stats['duration']
    print(f"  时间差异: {time_diff:.2f} 秒 ({time_diff/baseline_stats['duration']*100:.1f}%)")
    
    # 绘制对比图
    plot_comparison(main_stats, baseline_stats)
    
    print("\n" + "=" * 60)
    print("快速对比测试完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
