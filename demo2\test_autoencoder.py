import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
from feature_extractor import CustomImageQuantizerExtractor
from env_wrapper import CartPoleImageWrapper
import gym

def _entr(dist):
    dist = dist + 1e-7
    en_z_M = torch.mul(-1 * dist, torch.log(dist))
    en_z = torch.sum(torch.sum(en_z_M, dim=-1), dim=-1) / en_z_M.size(-2)
    return en_z

def save_reconstruction_images(observations, reconstructions, step, save_dir):
    """保存原始图像和重构图像的对比图"""
    # 将张量转换为numpy数组
    observations = observations.detach().cpu().numpy()
    reconstructions = reconstructions.detach().cpu().numpy()
    
    # 选择前4张图像进行对比
    n_images = min(4, observations.shape[0])
    
    # 创建图像网格
    fig, axes = plt.subplots(2, n_images, figsize=(4*n_images, 8))
    
    for i in range(n_images):
        # 显示原始图像
        if observations.shape[1] == 1:  # 灰度图
            axes[0, i].imshow(observations[i, 0], cmap='gray')
        else:  # RGB图
            axes[0, i].imshow(np.transpose(observations[i], (1, 2, 0)))
        axes[0, i].set_title(f'Original {i}')
        axes[0, i].axis('off')
        
        # 显示重构图像
        if reconstructions.shape[1] == 1:  # 灰度图
            axes[1, i].imshow(reconstructions[i, 0], cmap='gray')
        else:  # RGB图
            axes[1, i].imshow(np.transpose(reconstructions[i], (1, 2, 0)))
        axes[1, i].set_title(f'Reconstructed {i}')
        axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'reconstruction_step_{step}.png'))
    plt.close()

def main():
    # 配置参数
    IMG_HEIGHT = 64
    IMG_WIDTH = 64
    USE_GRAYSCALE = True
    NUM_EMBEDDINGS = 16
    EMBEDDING_DIM = 64
    GUMBEL_TAU = 1.0
    QAM_M_ARY = 16
    QAM_SNR_DB = 15.0
    
    # 创建保存目录
    save_dir = os.path.join("test_reconstruction_images", datetime.now().strftime("%Y%m%d_%H%M%S"))
    os.makedirs(save_dir, exist_ok=True)
    
    # 创建环境
    env = gym.make("CartPole-v1", render_mode="rgb_array")
    env = CartPoleImageWrapper(env, height=IMG_HEIGHT, width=IMG_WIDTH, grayscale=USE_GRAYSCALE)
    
    # 创建特征提取器
    feature_extractor = CustomImageQuantizerExtractor(
        observation_space=env.observation_space,
        num_embeddings=NUM_EMBEDDINGS,
        embedding_dim=EMBEDDING_DIM,
        gumbel_tau=GUMBEL_TAU,
        qam_m_ary=QAM_M_ARY,
        qam_snr_db=QAM_SNR_DB
    )
    
    # 训练参数
    num_episodes = 1000
    batch_size = 32
    learning_rate = 1e-4
    
    # 优化器
    optimizer = optim.Adam(feature_extractor.parameters(), lr=learning_rate)
    
    # 训练循环
    for episode in range(num_episodes):
        obs = env.reset()
        done = False
        episode_loss = 0
        step_count = 0
        
        while not done:
            # 收集一个batch的数据
            observations = []
            for _ in range(batch_size):
                if done:
                    obs = env.reset()
                observations.append(obs)
                obs, _, done, _ = env.step(env.action_space.sample())
            
            # 转换为tensor
            observations_tensor = torch.FloatTensor(np.array(observations)).to("cpu")
            
            # 前向传播
            encoded_features = feature_extractor.image_encoder(observations_tensor)
            quantized_output, dist_tensor = feature_extractor.quantizer.forward(encoded_features, mod=feature_extractor.qam_modem)
            recon_imgs = feature_extractor.image_decoder(quantized_output)
            
            # 计算损失
            loss_entr = _entr(dist_tensor)
            loss_mse = torch.mean(torch.square(observations_tensor - recon_imgs))
            loss = loss_mse + 0.001 * torch.mean(loss_entr)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(feature_extractor.parameters(), 1.0)
            optimizer.step()
            
            episode_loss += loss.item()
            step_count += 1
            
            # 每100步保存一次重构图像
            if step_count % 50 == 0:
                save_reconstruction_images(observations_tensor, recon_imgs, f"episode_{episode}_step_{step_count}", save_dir)
        
        # 打印每个episode的平均损失
        avg_loss = episode_loss / step_count
        print(f"Episode {episode + 1}/{num_episodes}, Average Loss: {avg_loss:.4f}")
    
    # 保存模型
    torch.save({
        'image_encoder_state_dict': feature_extractor.image_encoder.state_dict(),
        'quantizer_state_dict': feature_extractor.quantizer.state_dict(),
        'image_decoder_state_dict': feature_extractor.image_decoder.state_dict(),
        'optimizer_state_dict': optimizer.state_dict()
    }, os.path.join(save_dir, 'model_checkpoint.pth'))
    
    env.close()

if __name__ == "__main__":
    main() 