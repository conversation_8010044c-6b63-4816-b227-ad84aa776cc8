"""
PPO 实现的快速训练测试。
"""

import os
import sys

import gym
import numpy as np
import torch

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from env_wrapper import CartPoleImageWrapper
from networks.actor_critic import ActorCriticNetwork
from ppo_trainer import PPOTrainer
from rollout_buffer import RolloutBuffer


def quick_training_test():
    """运行最小步数的快速训练测试。"""
    print("开始快速训练测试...")

    # 配置
    config = {
        "img_height": 64,
        "img_width": 64,
        "use_grayscale": True,
        "latent_dim": 64,
        "num_embeddings": 16,
        "gumbel_tau": 1.0,
        "learning_rate": 3e-4,
        "n_steps": 32,  # 快速测试用小值
        "batch_size": 16,
        "n_epochs": 2,  # 快速测试用小值
        "gamma": 0.99,
        "gae_lambda": 0.95,
        "clip_range": 0.2,
        "entropy_coef": 0.01,
        "value_coef": 0.5,
        "recon_coef": 1.0,
        "quant_entropy_coef": 0.001,
        "max_grad_norm": 1.0,
        "total_timesteps": 100,  # 快速测试用很小的值
    }

    device = torch.device("cpu")  # 快速测试用 CPU

    # 设置环境
    env = gym.make("CartPole-v1", render_mode="rgb_array")
    env = CartPoleImageWrapper(
        env, height=config["img_height"], width=config["img_width"], grayscale=config["use_grayscale"]
    )

    obs_shape = env.observation_space.shape
    action_dim = env.action_space.n

    print(f"环境设置完成。观测形状: {obs_shape}, 动作维度: {action_dim}")

    # 创建模型
    model = ActorCriticNetwork(
        obs_shape=obs_shape,
        action_dim=action_dim,
        latent_dim=config["latent_dim"],
        num_embeddings=config["num_embeddings"],
        gumbel_tau=config["gumbel_tau"],
    ).to(device)

    print(f"模型创建完成。参数: {sum(p.numel() for p in model.parameters()):,}")

    # 创建训练器
    trainer = PPOTrainer(
        model=model,
        learning_rate=config["learning_rate"],
        clip_range=config["clip_range"],
        value_coef=config["value_coef"],
        entropy_coef=config["entropy_coef"],
        recon_coef=config["recon_coef"],
        quant_entropy_coef=config["quant_entropy_coef"],
        max_grad_norm=config["max_grad_norm"],
        device=device,
    )

    # 创建轨迹缓冲区
    rollout_buffer = RolloutBuffer(buffer_size=config["n_steps"], obs_shape=obs_shape, device=device)

    print("开始训练循环...")

    # 训练循环
    obs = env.reset()
    episode_rewards = []
    current_episode_reward = 0
    timestep = 0
    update_count = 0

    while timestep < config["total_timesteps"]:
        # 收集轨迹数据
        for step in range(config["n_steps"]):
            # 将观测转换为张量
            obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)

            # 从策略获取动作
            action, log_prob, value = model.act(obs_tensor)

            # 在环境中执行步骤
            next_obs, reward, done, info = env.step(action)

            # 存储转换
            rollout_buffer.add(
                obs=obs, action=action, log_prob=log_prob.item(), reward=reward, done=done, value=value.item()
            )

            # 更新状态
            obs = next_obs
            current_episode_reward += reward
            timestep += 1

            # 处理回合结束
            if done:
                episode_rewards.append(current_episode_reward)
                print(f"回合结束。奖励: {current_episode_reward}")
                current_episode_reward = 0
                obs = env.reset()

            # 检查是否收集了足够的数据或达到总步数
            if rollout_buffer.is_full() or timestep >= config["total_timesteps"]:
                break

        # 计算下一个价值用于自举
        if not done and timestep < config["total_timesteps"]:
            obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)
            _, _, next_value = model.act(obs_tensor)
            next_value = next_value.item()
        else:
            next_value = 0.0

        # 执行 PPO 更新
        print(f"执行更新 {update_count + 1}，包含 {len(rollout_buffer)} 个样本...")
        update_stats = trainer.update(
            rollout_buffer=rollout_buffer,
            n_epochs=config["n_epochs"],
            batch_size=config["batch_size"],
            gamma=config["gamma"],
            gae_lambda=config["gae_lambda"],
            next_value=next_value,
        )

        update_count += 1

        # 打印更新统计
        avg_reward = np.mean(episode_rewards[-5:]) if episode_rewards else 0
        print(f"更新 {update_count} | 步骤 {timestep} | 平均奖励: {avg_reward:.2f}")
        print(f"  策略损失: {update_stats['policy_loss']:.4f}")
        print(f"  价值损失: {update_stats['value_loss']:.4f}")
        print(f"  重构损失: {update_stats['recon_loss']:.4f}")
        print(f"  量化熵: {update_stats['quant_entropy_loss']:.4f}")
        print(f"  总损失: {update_stats['total_loss']:.4f}")

        # 清空缓冲区以进行下一次轨迹收集
        rollout_buffer.clear()

        if timestep >= config["total_timesteps"]:
            break

    print("\n训练完成！")
    print(f"总步数: {timestep}")
    print(f"总更新: {update_count}")
    print(f"总回合: {len(episode_rewards)}")
    if episode_rewards:
        print(f"平均奖励: {np.mean(episode_rewards):.2f}")
        print(f"最佳奖励: {max(episode_rewards):.2f}")

    env.close()

    # 测试重构
    print("\n测试重构...")
    with torch.no_grad():
        test_obs = torch.tensor(env.reset(), dtype=torch.float32, device=device).unsqueeze(0)
        recon = model.reconstruct(test_obs)
        recon_error = torch.mean((recon - test_obs) ** 2).item()
        print(f"重构 MSE: {recon_error:.6f}")

    print("快速训练测试成功完成！")


if __name__ == "__main__":
    quick_training_test()
