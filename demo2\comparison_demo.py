"""
Comparison demo showing the differences between stable_baselines3 and pure PyTorch implementations.
"""

import torch
import gym
import numpy as np
from datetime import datetime

# Import both implementations
from env_wrapper import CartPoleImageWrapper

# Pure PyTorch implementation
from models.actor_critic import ActorCriticNetwork as PurePyTorchModel
from ppo_trainer import PPOTrainer

# stable_baselines3 implementation
from feature_extractor import CustomImageQuantizerExtractor
from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv


def compare_model_architectures():
    """Compare the model architectures between implementations."""
    print("=" * 60)
    print("MODEL ARCHITECTURE COMPARISON")
    print("=" * 60)
    
    # Setup environment
    env = gym.make('CartPole-v1', render_mode='rgb_array')
    env = CartPoleImageWrapper(env, height=64, width=64, grayscale=True)
    obs_shape = env.observation_space.shape
    action_dim = env.action_space.n
    
    print(f"Environment: CartPole-v1 with image wrapper")
    print(f"Observation shape: {obs_shape}")
    print(f"Action dimension: {action_dim}")
    print()
    
    # Pure PyTorch model
    print("1. Pure PyTorch Implementation:")
    print("   - Unified ActorCriticNetwork")
    print("   - Shared encoder for policy and value")
    print("   - Integrated reconstruction capability")
    
    pytorch_model = PurePyTorchModel(
        obs_shape=obs_shape,
        action_dim=action_dim,
        latent_dim=64,
        num_embeddings=16
    )
    pytorch_params = sum(p.numel() for p in pytorch_model.parameters())
    print(f"   - Total parameters: {pytorch_params:,}")
    print()
    
    # stable_baselines3 model
    print("2. stable_baselines3 Implementation:")
    print("   - Separate feature extractor")
    print("   - PPO policy with custom features")
    print("   - Callback-based encoder training")
    
    # Create SB3 environment
    vec_env = DummyVecEnv([lambda: CartPoleImageWrapper(
        gym.make('CartPole-v1', render_mode='rgb_array'), 
        height=64, width=64, grayscale=True
    )])
    
    features_extractor_kwargs = dict(
        num_embeddings=16,
        embedding_dim=64,
        gumbel_tau=1.0,
        qam_m_ary=None,
        qam_snr_db=None,
    )
    
    policy_kwargs = dict(
        features_extractor_class=CustomImageQuantizerExtractor,
        features_extractor_kwargs=features_extractor_kwargs,
        net_arch=[],
    )
    
    sb3_model = PPO(
        "MlpPolicy",
        vec_env,
        policy_kwargs=policy_kwargs,
        learning_rate=3e-4,
        verbose=0
    )
    
    # Count SB3 parameters
    sb3_params = sum(p.numel() for p in sb3_model.policy.parameters())
    print(f"   - Total parameters: {sb3_params:,}")
    print()
    
    env.close()
    vec_env.close()


def compare_training_approaches():
    """Compare the training approaches."""
    print("=" * 60)
    print("TRAINING APPROACH COMPARISON")
    print("=" * 60)
    
    print("1. stable_baselines3 Approach:")
    print("   ✗ Separate optimization steps")
    print("   ✗ Callback-based encoder training")
    print("   ✗ Manual loss computation in callback")
    print("   ✗ Complex integration of auxiliary losses")
    print("   ✗ Limited control over training loop")
    print()
    
    print("2. Pure PyTorch Approach:")
    print("   ✓ Unified optimization step")
    print("   ✓ Integrated loss function")
    print("   ✓ Single backward pass for all losses")
    print("   ✓ Natural integration of auxiliary objectives")
    print("   ✓ Full control over training loop")
    print()


def compare_loss_functions():
    """Compare the loss function implementations."""
    print("=" * 60)
    print("LOSS FUNCTION COMPARISON")
    print("=" * 60)
    
    print("1. stable_baselines3 Implementation:")
    print("   - PPO loss: Computed by SB3 internally")
    print("   - Encoder loss: Computed in callback")
    print("   - Formula: V_mean + torch.mean(loss_entr)")
    print("   - Separate optimizers and backward passes")
    print()
    
    print("2. Pure PyTorch Implementation:")
    print("   - Unified loss function:")
    print("     total_loss = (")
    print("         policy_loss +")
    print("         value_coef * value_loss +")
    print("         entropy_coef * entropy_loss +")
    print("         recon_coef * recon_loss +")
    print("         quant_entropy_coef * quant_entropy_loss")
    print("     )")
    print("   - Single optimizer and backward pass")
    print()


def demonstrate_forward_pass():
    """Demonstrate forward pass differences."""
    print("=" * 60)
    print("FORWARD PASS DEMONSTRATION")
    print("=" * 60)
    
    # Setup
    env = gym.make('CartPole-v1', render_mode='rgb_array')
    env = CartPoleImageWrapper(env, height=64, width=64, grayscale=True)
    obs = env.reset()
    obs_tensor = torch.tensor(obs, dtype=torch.float32).unsqueeze(0)
    
    print(f"Input observation shape: {obs_tensor.shape}")
    print()
    
    # Pure PyTorch forward pass
    print("1. Pure PyTorch Forward Pass:")
    pytorch_model = PurePyTorchModel(
        obs_shape=env.observation_space.shape,
        action_dim=env.action_space.n,
        latent_dim=64,
        num_embeddings=16
    )
    
    with torch.no_grad():
        logits, values, z_q, dist_probs = pytorch_model(obs_tensor)
        recon = pytorch_model.reconstruct(obs_tensor)
    
    print(f"   - Action logits: {logits.shape}")
    print(f"   - State values: {values.shape}")
    print(f"   - Quantized features: {z_q.shape}")
    print(f"   - Distribution probs: {dist_probs.shape}")
    print(f"   - Reconstruction: {recon.shape}")
    print()
    
    # stable_baselines3 forward pass
    print("2. stable_baselines3 Forward Pass:")
    print("   - Features extraction: Separate step")
    print("   - Policy/Value: Through SB3 policy")
    print("   - Reconstruction: Manual through feature extractor")
    print("   - More complex data flow")
    print()
    
    env.close()


def show_code_complexity():
    """Show code complexity comparison."""
    print("=" * 60)
    print("CODE COMPLEXITY COMPARISON")
    print("=" * 60)
    
    print("1. stable_baselines3 Implementation:")
    print("   - Files: train_agent.py, feature_extractor.py, callback class")
    print("   - Lines of code: ~220 (main) + ~90 (extractor) + callback")
    print("   - Dependencies: stable_baselines3, custom callback")
    print("   - Complexity: High (multiple interacting components)")
    print()
    
    print("2. Pure PyTorch Implementation:")
    print("   - Files: train_pure_pytorch.py, models/, ppo_trainer.py, rollout_buffer.py")
    print("   - Lines of code: ~200 (main) + ~150 (model) + ~150 (trainer) + ~100 (buffer)")
    print("   - Dependencies: Only PyTorch and Gym")
    print("   - Complexity: Medium (clear separation of concerns)")
    print()
    
    print("Benefits of Pure PyTorch:")
    print("   ✓ More transparent and debuggable")
    print("   ✓ Easier to modify and extend")
    print("   ✓ Better integration of all components")
    print("   ✓ No hidden abstractions")
    print("   ✓ Full control over optimization")


def main():
    """Run all comparisons."""
    print("STABLE_BASELINES3 vs PURE PYTORCH PPO COMPARISON")
    print("=" * 60)
    print(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        compare_model_architectures()
        compare_training_approaches()
        compare_loss_functions()
        demonstrate_forward_pass()
        show_code_complexity()
        
        print("=" * 60)
        print("CONCLUSION")
        print("=" * 60)
        print("The pure PyTorch implementation provides:")
        print("• Better integration of encoder and RL training")
        print("• More transparent and controllable training process")
        print("• Simplified dependencies and reduced complexity")
        print("• Easier debugging and modification")
        print("• Maintained all original functionality")
        print()
        print("Recommendation: Use pure PyTorch implementation for")
        print("research and development where flexibility and")
        print("transparency are important.")
        
    except Exception as e:
        print(f"Error during comparison: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
