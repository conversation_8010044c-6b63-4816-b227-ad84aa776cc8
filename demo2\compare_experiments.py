"""
对比实验脚本 - 比较有无信道传输的性能差异。
"""

import os
import subprocess
import time
import json
import matplotlib.pyplot as plt
import numpy as np
import torch
from torch.utils.tensorboard import SummaryWriter
import argparse


def run_experiment(script_name, experiment_name, total_timesteps=50000):
    """运行单个实验"""
    print(f"\n{'='*60}")
    print(f"开始运行 {experiment_name}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    # 修改脚本中的训练步数 (简化版本，实际可以通过参数传递)
    try:
        result = subprocess.run(
            ["python", script_name],
            cwd=".",
            capture_output=True,
            text=True,
            timeout=3600  # 1小时超时
        )
        
        if result.returncode == 0:
            print(f"{experiment_name} 完成成功")
        else:
            print(f"{experiment_name} 失败: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print(f"{experiment_name} 超时")
    except Exception as e:
        print(f"{experiment_name} 出错: {e}")
    
    end_time = time.time()
    duration = end_time - start_time
    print(f"{experiment_name} 耗时: {duration:.2f} 秒")
    
    return duration


def load_tensorboard_data(log_dir, tag):
    """从 TensorBoard 日志中加载数据"""
    try:
        from tensorboard.backend.event_processing.event_accumulator import EventAccumulator
        
        ea = EventAccumulator(log_dir)
        ea.Reload()
        
        if tag in ea.Tags()['scalars']:
            scalar_events = ea.Scalars(tag)
            steps = [event.step for event in scalar_events]
            values = [event.value for event in scalar_events]
            return steps, values
        else:
            print(f"标签 {tag} 不存在于 {log_dir}")
            return [], []
            
    except ImportError:
        print("需要安装 tensorboard 来读取日志数据")
        return [], []
    except Exception as e:
        print(f"读取 TensorBoard 数据失败: {e}")
        return [], []


def compare_results():
    """比较两个实验的结果"""
    print("\n" + "="*60)
    print("比较实验结果")
    print("="*60)
    
    # 检查模型文件是否存在
    main_model_path = "./model.pth"
    baseline_model_path = "./baseline_model.pth"
    
    results = {}
    
    # 加载主实验结果
    if os.path.exists(main_model_path):
        try:
            main_checkpoint = torch.load(main_model_path, map_location='cpu')
            results['main'] = {
                'timesteps': main_checkpoint.get('timestep', 0),
                'config': main_checkpoint.get('config', {}),
                'model_params': sum(p.numel() for p in torch.load(main_model_path, map_location='cpu')['model_state_dict'].values())
            }
            print(f"✅ 主实验 (有量化器): {results['main']['timesteps']} 步")
        except Exception as e:
            print(f"❌ 加载主实验结果失败: {e}")
    else:
        print("❌ 主实验模型文件不存在")
    
    # 加载基线实验结果
    if os.path.exists(baseline_model_path):
        try:
            baseline_checkpoint = torch.load(baseline_model_path, map_location='cpu')
            results['baseline'] = {
                'timesteps': baseline_checkpoint.get('timestep', 0),
                'config': baseline_checkpoint.get('config', {}),
                'model_params': sum(p.numel() for p in torch.load(baseline_model_path, map_location='cpu')['model_state_dict'].values())
            }
            print(f"✅ 基线实验 (无量化器): {results['baseline']['timesteps']} 步")
        except Exception as e:
            print(f"❌ 加载基线实验结果失败: {e}")
    else:
        print("❌ 基线实验模型文件不存在")
    
    # 比较模型参数数量
    if 'main' in results and 'baseline' in results:
        print(f"\n模型参数对比:")
        print(f"  主实验 (有量化器): {results['main']['model_params']:,} 参数")
        print(f"  基线实验 (无量化器): {results['baseline']['model_params']:,} 参数")
        param_diff = results['main']['model_params'] - results['baseline']['model_params']
        print(f"  参数差异: {param_diff:,} ({param_diff/results['baseline']['model_params']*100:.1f}%)")
    
    # 尝试从 TensorBoard 日志比较训练曲线
    try:
        plot_comparison()
    except Exception as e:
        print(f"绘制对比图失败: {e}")
    
    return results


def plot_comparison():
    """绘制对比图表"""
    print("\n绘制训练曲线对比...")
    
    # 加载训练数据
    main_steps, main_rewards = load_tensorboard_data("./logs/", "Environment/Average_Reward")
    baseline_steps, baseline_rewards = load_tensorboard_data("./baseline_logs/", "Baseline/Average_Reward")
    
    if main_steps and baseline_steps:
        plt.figure(figsize=(12, 8))
        
        # 奖励对比
        plt.subplot(2, 2, 1)
        plt.plot(main_steps, main_rewards, label='有量化器和信道', color='blue', alpha=0.8)
        plt.plot(baseline_steps, baseline_rewards, label='基线 (无量化器)', color='red', alpha=0.8)
        plt.xlabel('训练步数')
        plt.ylabel('平均奖励')
        plt.title('训练奖励对比')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 加载损失数据
        main_policy_steps, main_policy_loss = load_tensorboard_data("./logs/", "Training/policy_loss")
        baseline_policy_steps, baseline_policy_loss = load_tensorboard_data("./baseline_logs/", "Baseline_Training/policy_loss")
        
        if main_policy_steps and baseline_policy_steps:
            plt.subplot(2, 2, 2)
            plt.plot(main_policy_steps, main_policy_loss, label='有量化器和信道', color='blue', alpha=0.8)
            plt.plot(baseline_policy_steps, baseline_policy_loss, label='基线 (无量化器)', color='red', alpha=0.8)
            plt.xlabel('训练步数')
            plt.ylabel('策略损失')
            plt.title('策略损失对比')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        # 重构损失对比
        main_recon_steps, main_recon_loss = load_tensorboard_data("./logs/", "Training/recon_loss")
        baseline_recon_steps, baseline_recon_loss = load_tensorboard_data("./baseline_logs/", "Baseline_Training/recon_loss")
        
        if main_recon_steps and baseline_recon_steps:
            plt.subplot(2, 2, 3)
            plt.plot(main_recon_steps, main_recon_loss, label='有量化器和信道', color='blue', alpha=0.8)
            plt.plot(baseline_recon_steps, baseline_recon_loss, label='基线 (无量化器)', color='red', alpha=0.8)
            plt.xlabel('训练步数')
            plt.ylabel('重构损失')
            plt.title('重构损失对比')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        # 最终性能对比 (柱状图)
        plt.subplot(2, 2, 4)
        if main_rewards and baseline_rewards:
            final_main = np.mean(main_rewards[-10:]) if len(main_rewards) >= 10 else np.mean(main_rewards)
            final_baseline = np.mean(baseline_rewards[-10:]) if len(baseline_rewards) >= 10 else np.mean(baseline_rewards)
            
            methods = ['有量化器\n和信道', '基线\n(无量化器)']
            performance = [final_main, final_baseline]
            colors = ['blue', 'red']
            
            bars = plt.bar(methods, performance, color=colors, alpha=0.7)
            plt.ylabel('最终平均奖励')
            plt.title('最终性能对比')
            plt.grid(True, alpha=0.3, axis='y')
            
            # 添加数值标签
            for bar, perf in zip(bars, performance):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                        f'{perf:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('./experiment_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("对比图已保存为 experiment_comparison.png")
    else:
        print("无法加载训练数据进行对比")


def main():
    parser = argparse.ArgumentParser(description='运行对比实验')
    parser.add_argument('--mode', choices=['both', 'main', 'baseline', 'compare'], 
                       default='both', help='运行模式')
    parser.add_argument('--timesteps', type=int, default=50000, 
                       help='训练步数')
    
    args = parser.parse_args()
    
    print("PPO + 编码器训练对比实验")
    print("="*60)
    print("实验设置:")
    print(f"  主实验: 使用量化器和信道传输")
    print(f"  基线实验: 不使用量化器和信道传输")
    print(f"  训练步数: {args.timesteps}")
    print(f"  运行模式: {args.mode}")
    
    if args.mode in ['both', 'main']:
        print("\n开始主实验...")
        run_experiment("train.py", "主实验 (有量化器和信道)", args.timesteps)
    
    if args.mode in ['both', 'baseline']:
        print("\n开始基线实验...")
        run_experiment("train_baseline.py", "基线实验 (无量化器)", args.timesteps)
    
    if args.mode in ['both', 'compare']:
        print("\n开始结果比较...")
        results = compare_results()
        
        # 保存比较结果
        with open('comparison_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print("比较结果已保存到 comparison_results.json")
    
    print("\n" + "="*60)
    print("对比实验完成！")
    print("="*60)
    print("结果文件:")
    print("  - model.pth: 主实验模型")
    print("  - baseline_model.pth: 基线实验模型")
    print("  - logs/: 主实验 TensorBoard 日志")
    print("  - baseline_logs/: 基线实验 TensorBoard 日志")
    print("  - experiment_comparison.png: 对比图表")
    print("  - comparison_results.json: 详细比较结果")


if __name__ == "__main__":
    main()
