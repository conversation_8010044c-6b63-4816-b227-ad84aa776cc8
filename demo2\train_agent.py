import os

import gym
import matplotlib
import numpy as np
import torch

matplotlib.use("Agg")  # 设置后端为 Agg
from datetime import datetime

import matplotlib.pyplot as plt

# from stable_baselines3.common.callbacks import EvalCallback, StopTrainingOnRewardThreshold # Optional
from env_wrapper import CartPoleImageWrapper
from feature_extractor import CustomImageQuantizerExtractor
from stable_baselines3 import PPO
from stable_baselines3.common.callbacks import BaseCallback
from stable_baselines3.common.vec_env import DummyVecEnv

# torch.autograd.set_detect_anomaly(True)


def _entr(dist):
    dist = dist + 1e-7
    en_z_M = torch.mul(-1 * dist, torch.log(dist))
    en_z = torch.sum(torch.sum(en_z_M, dim=-1), dim=-1) / en_z_M.size(-2)
    return en_z


class CustomFeatureExtractorCallback(BaseCallback):
    def __init__(self, n_steps_interval=1000, verbose=0):
        super().__init__(verbose)
        self.n_steps_interval = n_steps_interval
        self.step_count = 0
        self.idx = 0

    def save_reconstruction_images(self, observations_tensor, recon_imgs, step):
        """保存原始图像和重构图像的对比图"""
        # 将张量转换为numpy数组
        observations = observations_tensor.detach().cpu().numpy()
        reconstructions = recon_imgs.detach().cpu().numpy()

        # 选择前4张图像进行对比
        n_images = min(4, observations.shape[0])

        # 创建图像网格
        fig, axes = plt.subplots(2, n_images, figsize=(4 * n_images, 8))

        for i in range(n_images):
            # 显示原始图像
            if observations.shape[1] == 1:  # 灰度图
                axes[0, i].imshow(observations[i, 0], cmap="gray")
            else:  # RGB图
                axes[0, i].imshow(np.transpose(observations[i], (1, 2, 0)))
            axes[0, i].set_title(f"Original {i}")
            axes[0, i].axis("off")

            # 显示重构图像
            if reconstructions.shape[1] == 1:  # 灰度图
                axes[1, i].imshow(reconstructions[i, 0], cmap="gray")
            else:  # RGB图
                axes[1, i].imshow(np.transpose(reconstructions[i], (1, 2, 0)))
            axes[1, i].set_title(f"Reconstructed {i}")
            axes[1, i].axis("off")

        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, f"reconstruction_step_{step}.png"))
        plt.close()

    def _on_step(self) -> bool:
        self.step_count += 1

        if self.step_count % self.n_steps_interval == 0:
            self.idx += 1
            feature_extractor = self.model.policy.features_extractor

            # 获取当前观测用于评估网络
            current_obs = self.locals.get("obs_tensor", None)
            if current_obs is not None:
                # 获取评估网络的V值
                with torch.no_grad():
                    v_values = self.model.policy.predict_values(current_obs)
                    v_mean = torch.mean(v_values)
            else:
                # 如果无法获取当前观测，使用默认值
                v_mean = torch.tensor(0.0, device=feature_extractor.image_encoder.parameters().__next__().device)

            # 获取量化器的分布用于计算熵损失
            # 需要通过feature_extractor获取最近的分布信息
            if hasattr(feature_extractor, "last_dist_tensor") and feature_extractor.last_dist_tensor is not None:
                dist_tensor = feature_extractor.last_dist_tensor
                loss_entr = _entr(dist_tensor)
            else:
                # 如果没有分布信息，创建一个默认的熵损失
                loss_entr = torch.tensor(0.0, device=feature_extractor.image_encoder.parameters().__next__().device)

            # 新的损失函数：V值 + 熵损失
            loss = v_mean + torch.mean(loss_entr)

            feature_extractor.optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(feature_extractor.image_encoder.parameters(), 1.0)
            feature_extractor.optimizer.step()

        return True


def main():
    # --- Configuration ---
    IMG_HEIGHT = 64
    IMG_WIDTH = 64
    USE_GRAYSCALE = True

    # Quantizer
    NUM_EMBEDDINGS = 16  # Codebook size. MUST BE <= QAM_M_ARY if QAM is active.
    EMBEDDING_DIM = 64  # Dimension of each embedding; features_dim for SB3
    GUMBEL_TAU = 1.0

    # QAM Channel (optional)
    # Set QAM_M_ARY and QAM_PSNR_DB to None to disable QAM channel effects.
    QAM_M_ARY = 16  # e.g., 4, 16, 64. Must be a perfect square.
    # If None, QAM is disabled.
    QAM_SNR_DB = 15.0  # Peak Signal to Noise Ratio in dB.
    # If None (along with QAM_M_ARY), QAM is disabled.
    # To disable QAM:
    # QAM_M_ARY = None
    # QAM_PSNR_DB = None

    # PPO Agent
    PPO_LEARNING_RATE = 3e-4
    PPO_N_STEPS = 1024
    PPO_BATCH_SIZE = 64
    PPO_N_EPOCHS = 10
    PPO_GAMMA = 0.99
    PPO_GAE_LAMBDA = 0.95
    PPO_CLIP_RANGE = 0.2
    TOTAL_TIMESTEPS = 200000  # Reduce for quicker testing if needed

    NET_ARCH_PI_VF = []

    TENSORBOARD_LOG_DIR = "./ppo_cartpole_qam_tensorboard/"
    MODEL_SAVE_PATH = "ppo_cartpole_pomdp_qam"

    # --- Validate K <= M for QAM ---
    if QAM_M_ARY is not None and NUM_EMBEDDINGS > QAM_M_ARY:
        raise ValueError(
            f"Configuration error: NUM_EMBEDDINGS_K ({NUM_EMBEDDINGS}) cannot be greater than QAM_M_ARY ({QAM_M_ARY})."
        )

    # --- Setup Environment ---
    def make_env():
        env = gym.make("CartPole-v1", render_mode="rgb_array")
        env = CartPoleImageWrapper(env, height=IMG_HEIGHT, width=IMG_WIDTH, grayscale=USE_GRAYSCALE)
        return env

    vec_env = DummyVecEnv([make_env])

    # --- Configure Custom Feature Extractor ---
    features_extractor_kwargs = dict(
        num_embeddings=NUM_EMBEDDINGS,
        embedding_dim=EMBEDDING_DIM,
        gumbel_tau=GUMBEL_TAU,
        qam_m_ary=QAM_M_ARY,  # Pass QAM M-ary value
        qam_snr_db=QAM_SNR_DB,  # Pass QAM PSNR
    )

    policy_kwargs = dict(
        features_extractor_class=CustomImageQuantizerExtractor,
        features_extractor_kwargs=features_extractor_kwargs,
        net_arch=NET_ARCH_PI_VF,
    )

    # --- Initialize PPO Agent ---
    model = PPO(
        "MlpPolicy",
        vec_env,
        policy_kwargs=policy_kwargs,
        learning_rate=PPO_LEARNING_RATE,
        n_steps=PPO_N_STEPS,
        batch_size=PPO_BATCH_SIZE,
        n_epochs=PPO_N_EPOCHS,
        gamma=PPO_GAMMA,
        gae_lambda=PPO_GAE_LAMBDA,
        clip_range=PPO_CLIP_RANGE,
        verbose=1,
        tensorboard_log=TENSORBOARD_LOG_DIR,
    )

    print("--- Agent Configuration ---")
    print(f"Image: {IMG_HEIGHT}x{IMG_WIDTH}, Grayscale: {USE_GRAYSCALE}")
    print(f"Quantizer K: {NUM_EMBEDDINGS}, D: {EMBEDDING_DIM}, Tau: {GUMBEL_TAU}")
    if QAM_M_ARY is not None and QAM_SNR_DB is not None:
        print(f"QAM Channel: M-ary={QAM_M_ARY}, PSNR={QAM_SNR_DB}dB")
    else:
        print("QAM Channel: Disabled")
    print(f"PPO Timesteps: {TOTAL_TIMESTEPS}")
    print(f"Policy/Value Net Arch: {NET_ARCH_PI_VF}")
    print("---------------------------")

    # --- Train the Agent ---
    print(f"\nStarting training for {TOTAL_TIMESTEPS} timesteps...")
    encoder_callback = CustomFeatureExtractorCallback(n_steps_interval=5)  # 改为每5000步保存一次
    try:
        model.learn(total_timesteps=TOTAL_TIMESTEPS, progress_bar=True, callback=encoder_callback)
    except Exception as e:
        print(f"An error occurred during training: {e}")
        import traceback

        traceback.print_exc()
    finally:
        model.save(MODEL_SAVE_PATH)
        print(f"\nTraining finished. Model saved to {MODEL_SAVE_PATH}")
        vec_env.close()

    print("\n--- To view TensorBoard logs, run: ---")
    print(f"tensorboard --logdir={TENSORBOARD_LOG_DIR}")


if __name__ == "__main__":
    main()
