# 项目结构说明

## 目录结构

```
demo2/
├── networks/                    # 神经网络模块
│   ├── __init__.py
│   ├── actor_critic.py         # 统一的 Actor-Critic 网络
│   ├── baseline_actor_critic.py # 基线 Actor-Critic 网络 (无量化器)
│   └── encoder_decoder.py      # 编码器/解码器包装模块
├── tests/                      # 测试模块
│   ├── __init__.py
│   ├── test_implementation.py  # 完整功能测试
│   ├── quick_train_test.py     # 快速训练测试
│   └── quick_comparison_test.py # 快速对比测试
├── __init__.py                 # 包初始化
├── channel.py                  # QAM/PSK 信道仿真
├── env_wrapper.py              # 环境包装器 (图像预处理)
├── image_encoder.py            # DTJSCC 编码器/解码器实现
├── ppo_trainer.py              # PPO 训练器
├── baseline_trainer.py         # 基线 PPO 训练器 (无量化器)
├── quantizer.py                # Gumbel-Softmax 量化器
├── rollout_buffer.py           # 轨迹数据缓冲区
├── train.py                    # 主训练脚本 (有量化器和信道)
├── train_baseline.py           # 基线训练脚本 (无量化器)
├── compare_experiments.py      # 对比实验脚本
├── verify_setup.py             # 环境验证脚本
├── README.md                   # 项目文档
├── 重构总结.md                 # 重构总结
├── 使用指南.md                 # 详细使用指南
└── 项目结构.md                 # 本文件
```

## 核心文件说明

### 主要模块

1. **train.py** - 主训练脚本
   - 配置所有超参数
   - 设置环境和模型
   - 执行完整的训练循环
   - 保存模型和日志

2. **networks/actor_critic.py** - 核心网络
   - 统一的 Actor-Critic 架构
   - 集成编码器、量化器、策略头和价值头
   - 支持动作采样和重构

3. **ppo_trainer.py** - 训练逻辑
   - 纯 PyTorch PPO 实现
   - 集成损失函数 (策略 + 价值 + 重构 + 量化熵)
   - 梯度裁剪和优化

4. **rollout_buffer.py** - 数据管理
   - 轨迹数据收集和存储
   - GAE 优势估计计算
   - 小批次采样

### 支持模块

5. **networks/encoder_decoder.py** - 网络包装
   - 现有编码器/解码器的包装器
   - 处理导入和接口统一

6. **image_encoder.py** - 图像处理网络
   - DTJSCC_CIFAR10_Encoder/Decoder 实现
   - 修改为支持全局平均池化

7. **quantizer.py** - 量化模块
   - Gumbel-Softmax 量化器
   - 支持 QAM 信道仿真

8. **env_wrapper.py** - 环境包装
   - CartPole 环境的图像包装器
   - 图像预处理和格式转换

9. **channel.py** - 信道仿真
   - PSK/QAM 调制解调
   - AWGN 信道噪声

### 测试模块

10. **tests/test_implementation.py** - 完整测试
    - 模型创建测试
    - 前向传播测试
    - 训练器功能测试
    - 环境交互测试

11. **tests/quick_train_test.py** - 快速验证
    - 小规模训练测试
    - 验证训练循环正确性
    - 快速功能验证

## 使用流程

### 1. 开发和测试
```bash
# 运行完整测试套件
python tests/test_implementation.py

# 快速训练验证
python tests/quick_train_test.py
```

### 2. 训练模型
```bash
# 完整训练 (可在 train.py 中修改配置)
python train.py
```

### 3. 自定义配置
在 `train.py` 中修改 `config` 字典：
```python
config = {
    'latent_dim': 64,           # 潜在维度
    'num_embeddings': 16,       # 码本大小
    'learning_rate': 3e-4,      # 学习率
    'recon_coef': 1.0,          # 重构损失权重
    'quant_entropy_coef': 0.001, # 量化熵权重
    # ... 更多配置
}
```

## 设计特点

### 模块化设计
- 清晰的职责分离
- 易于测试和调试
- 便于扩展和修改

### 统一训练
- 单一损失函数
- 单次反向传播
- 集成优化

### 保留兼容性
- 重用现有编码器/解码器
- 保持原有功能
- 支持 QAM 信道

### 易于使用
- 简单的配置接口
- 完整的测试覆盖
- 详细的文档说明

## 扩展指南

### 添加新的编码器
1. 在 `networks/encoder_decoder.py` 中添加新的包装类
2. 在 `networks/actor_critic.py` 中使用新编码器
3. 更新测试以验证新功能

### 修改损失函数
1. 在 `ppo_trainer.py` 的 `compute_ppo_loss` 方法中修改
2. 添加新的损失项和权重
3. 在配置中添加相应的超参数

### 支持新环境
1. 在 `env_wrapper.py` 中添加新的包装器
2. 修改 `train.py` 中的环境设置
3. 调整网络输入输出维度

这个项目结构提供了一个清晰、模块化、易于扩展的 PPO + 编码器训练框架。
