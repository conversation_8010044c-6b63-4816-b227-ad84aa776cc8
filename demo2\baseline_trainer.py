"""
基线 PPO 训练器，不使用量化器和信道传输。
"""

from typing import Any, Dict, Optional, Tuple

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from networks.baseline_actor_critic import BaselineActorCriticNetwork
from rollout_buffer import MiniBatchSampler, RolloutBuffer
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm


class BaselinePPOTrainer:
    """
    基线 PPO 训练器，不使用量化器和信道传输。
    """

    def __init__(
        self,
        model: BaselineActorCriticNetwork,
        learning_rate: float = 3e-4,
        clip_range: float = 0.2,
        value_coef: float = 0.5,
        entropy_coef: float = 0.01,
        recon_coef: float = 1.0,
        max_grad_norm: float = 1.0,
        device: torch.device = torch.device("cpu"),
    ):
        """
        Args:
            model: 基线 Actor-Critic 网络
            learning_rate: 优化器学习率
            clip_range: PPO 裁剪范围
            value_coef: 价值损失系数
            entropy_coef: 熵损失系数
            recon_coef: 重构损失系数
            max_grad_norm: 梯度裁剪的最大范数
            device: 训练设备
        """
        self.model = model
        self.device = device

        # PPO 超参数
        self.clip_range = clip_range
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.recon_coef = recon_coef
        self.max_grad_norm = max_grad_norm

        # 优化器
        self.optimizer = optim.Adam(model.parameters(), lr=learning_rate)

        # 训练统计
        self.training_stats = {
            "policy_loss": [],
            "value_loss": [],
            "entropy_loss": [],
            "recon_loss": [],
            "total_loss": [],
            "approx_kl": [],
            "clip_fraction": [],
        }

    def compute_ppo_loss(
        self,
        obs: torch.Tensor,
        actions: torch.Tensor,
        old_log_probs: torch.Tensor,
        advantages: torch.Tensor,
        returns: torch.Tensor,
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        计算基线 PPO 损失 (不包含量化器损失)。

        Args:
            obs: 观测
            actions: 执行的动作
            old_log_probs: 旧策略的对数概率
            advantages: 计算的优势
            returns: 计算的回报

        Returns:
            (总损失, 损失字典) 的元组
        """
        # 通过模型前向传播
        logits, values, latent = self.model(obs)

        # 用当前策略评估动作
        new_log_probs, _, entropy = self.model.evaluate_actions(obs, actions)

        # PPO 策略损失
        ratio = torch.exp(new_log_probs - old_log_probs)
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1 - self.clip_range, 1 + self.clip_range) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()

        # 价值损失
        value_loss = 0.5 * (values.squeeze() - returns).pow(2).mean()

        # 熵损失 (负号因为我们想要最大化熵)
        entropy_loss = -entropy.mean()

        # 重构损失
        recon_imgs = self.model.decoder(latent)
        recon_loss = ((recon_imgs - obs) ** 2).mean()

        # 总损失 (不包含量化器熵损失)
        total_loss = (
            policy_loss
            + self.value_coef * value_loss
            + self.entropy_coef * entropy_loss
            + self.recon_coef * recon_loss
        )

        # 计算额外统计信息
        with torch.no_grad():
            approx_kl = ((old_log_probs - new_log_probs) ** 2).mean()
            clip_fraction = ((ratio - 1.0).abs() > self.clip_range).float().mean()

        loss_dict = {
            "policy_loss": policy_loss,
            "value_loss": value_loss,
            "entropy_loss": entropy_loss,
            "recon_loss": recon_loss,
            "total_loss": total_loss,
            "approx_kl": approx_kl,
            "clip_fraction": clip_fraction,
        }

        return total_loss, loss_dict

    def update(
        self,
        rollout_buffer: RolloutBuffer,
        n_epochs: int = 10,
        batch_size: int = 64,
        gamma: float = 0.99,
        gae_lambda: float = 0.95,
        next_value: float = 0.0,
    ) -> Dict[str, float]:
        """
        执行基线 PPO 更新。

        Args:
            rollout_buffer: 包含轨迹数据的缓冲区
            n_epochs: 优化轮数
            batch_size: 小批次大小
            gamma: 折扣因子
            gae_lambda: GAE lambda
            next_value: 下一状态的价值用于自举

        Returns:
            训练统计字典
        """
        # 从缓冲区获取处理后的数据
        obs, actions, old_log_probs, advantages, returns = rollout_buffer.get_data(
            gamma=gamma, gae_lambda=gae_lambda, next_value=next_value
        )

        data_size = len(obs)
        epoch_stats = {key: [] for key in self.training_stats.keys()}

        # 计算总的小批次数量
        total_batches = n_epochs * ((data_size + batch_size - 1) // batch_size)

        # 创建训练进度条
        train_pbar = tqdm(
            total=total_batches, 
            desc="基线 PPO 训练", 
            unit="批次", 
            ncols=80, 
            position=2, 
            leave=False
        )

        # 执行多轮优化
        for epoch in range(n_epochs):
            # 创建小批次采样器
            sampler = MiniBatchSampler(batch_size, data_size)

            for batch_indices in sampler:
                # 获取小批次数据
                batch_obs = obs[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]

                # 计算损失
                total_loss, loss_dict = self.compute_ppo_loss(
                    batch_obs, batch_actions, batch_old_log_probs, batch_advantages, batch_returns
                )

                # 优化步骤
                self.optimizer.zero_grad()
                total_loss.backward()
                nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)
                self.optimizer.step()

                # 记录统计信息
                for key, value in loss_dict.items():
                    epoch_stats[key].append(value.item())

                # 更新进度条
                train_pbar.update(1)
                train_pbar.set_postfix({"轮次": f"{epoch + 1}/{n_epochs}", "损失": f"{total_loss.item():.4f}"})

        train_pbar.close()

        # 计算平均统计信息
        avg_stats = {}
        for key, values in epoch_stats.items():
            avg_value = np.mean(values)
            avg_stats[key] = avg_value
            self.training_stats[key].append(avg_value)

        return avg_stats
