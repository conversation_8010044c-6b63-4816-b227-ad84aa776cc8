"""
Pure PyTorch PPO training script with integrated encoder training.
"""

import os
import gym
import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from torch.utils.tensorboard import SummaryWriter

# Import local modules
from env_wrapper import CartPoleImageWrapper
from models.actor_critic import ActorCriticNetwork
from ppo_trainer import PPOTrainer
from rollout_buffer import RolloutBuffer
from channel import PSK  # For QAM modulation


def save_reconstruction_images(original_imgs, recon_imgs, step, save_dir):
    """Save comparison of original and reconstructed images."""
    os.makedirs(save_dir, exist_ok=True)
    
    # Convert tensors to numpy
    if isinstance(original_imgs, torch.Tensor):
        original_imgs = original_imgs.detach().cpu().numpy()
    if isinstance(recon_imgs, torch.Tensor):
        recon_imgs = recon_imgs.detach().cpu().numpy()
    
    # Select first 4 images for comparison
    n_images = min(4, original_imgs.shape[0])
    
    fig, axes = plt.subplots(2, n_images, figsize=(4 * n_images, 8))
    if n_images == 1:
        axes = axes.reshape(2, 1)
    
    for i in range(n_images):
        # Original image
        if original_imgs.shape[1] == 1:  # Grayscale
            axes[0, i].imshow(original_imgs[i, 0], cmap='gray')
        else:  # RGB
            axes[0, i].imshow(np.transpose(original_imgs[i], (1, 2, 0)))
        axes[0, i].set_title(f'Original {i}')
        axes[0, i].axis('off')
        
        # Reconstructed image
        if recon_imgs.shape[1] == 1:  # Grayscale
            axes[1, i].imshow(recon_imgs[i, 0], cmap='gray')
        else:  # RGB
            axes[1, i].imshow(np.transpose(recon_imgs[i], (1, 2, 0)))
        axes[1, i].set_title(f'Reconstructed {i}')
        axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'reconstruction_step_{step}.png'))
    plt.close()


def main():
    # Configuration
    config = {
        # Environment
        'img_height': 64,
        'img_width': 64,
        'use_grayscale': True,
        
        # Model
        'latent_dim': 64,
        'num_embeddings': 16,
        'gumbel_tau': 1.0,
        
        # QAM Channel (optional)
        'qam_m_ary': 16,
        'qam_snr_db': 15.0,
        
        # PPO
        'learning_rate': 3e-4,
        'n_steps': 1024,
        'batch_size': 64,
        'n_epochs': 10,
        'gamma': 0.99,
        'gae_lambda': 0.95,
        'clip_range': 0.2,
        'entropy_coef': 0.01,
        'value_coef': 0.5,
        'recon_coef': 1.0,
        'quant_entropy_coef': 0.001,
        'max_grad_norm': 1.0,
        
        # Training
        'total_timesteps': 200000,
        'log_interval': 1000,
        'save_interval': 10000,
        'eval_interval': 5000,
        
        # Logging
        'tensorboard_log': './pure_pytorch_ppo_logs/',
        'model_save_path': './pure_pytorch_ppo_model.pth',
        'reconstruction_save_dir': './pure_pytorch_reconstruction_images/'
    }
    
    # Device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Validate configuration
    if config['qam_m_ary'] is not None and config['num_embeddings'] > config['qam_m_ary']:
        raise ValueError(
            f"num_embeddings ({config['num_embeddings']}) cannot exceed qam_m_ary ({config['qam_m_ary']})"
        )
    
    # Setup environment
    env = gym.make('CartPole-v1', render_mode='rgb_array')
    env = CartPoleImageWrapper(
        env, 
        height=config['img_height'], 
        width=config['img_width'], 
        grayscale=config['use_grayscale']
    )
    
    obs_shape = env.observation_space.shape  # (C, H, W)
    action_dim = env.action_space.n
    
    print(f"Environment: CartPole-v1")
    print(f"Observation shape: {obs_shape}")
    print(f"Action dimension: {action_dim}")
    
    # Setup QAM modulator (optional)
    qam_modem = None
    if config['qam_m_ary'] is not None and config['qam_snr_db'] is not None:
        qam_modem = PSK(M=config['qam_m_ary'], SNR=config['qam_snr_db'])
        print(f"QAM Channel: M-ary={config['qam_m_ary']}, SNR={config['qam_snr_db']}dB")
    else:
        print("QAM Channel: Disabled")
    
    # Create model
    model = ActorCriticNetwork(
        obs_shape=obs_shape,
        action_dim=action_dim,
        latent_dim=config['latent_dim'],
        num_embeddings=config['num_embeddings'],
        gumbel_tau=config['gumbel_tau'],
        qam_modem=qam_modem
    ).to(device)
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Create trainer
    trainer = PPOTrainer(
        model=model,
        learning_rate=config['learning_rate'],
        clip_range=config['clip_range'],
        value_coef=config['value_coef'],
        entropy_coef=config['entropy_coef'],
        recon_coef=config['recon_coef'],
        quant_entropy_coef=config['quant_entropy_coef'],
        max_grad_norm=config['max_grad_norm'],
        device=device
    )
    
    # Create rollout buffer
    rollout_buffer = RolloutBuffer(
        buffer_size=config['n_steps'],
        obs_shape=obs_shape,
        device=device
    )
    
    # Setup logging
    os.makedirs(config['tensorboard_log'], exist_ok=True)
    os.makedirs(config['reconstruction_save_dir'], exist_ok=True)
    writer = SummaryWriter(config['tensorboard_log'])
    
    # Training loop
    obs = env.reset()
    episode_rewards = []
    current_episode_reward = 0
    timestep = 0
    episode_count = 0
    
    print(f"\nStarting training for {config['total_timesteps']} timesteps...")
    print("=" * 50)
    
    while timestep < config['total_timesteps']:
        # Collect rollout data
        for step in range(config['n_steps']):
            # Convert observation to tensor
            obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)
            
            # Get action from policy
            action, log_prob, value = model.act(obs_tensor)
            
            # Take step in environment
            next_obs, reward, done, info = env.step(action)
            
            # Store transition
            rollout_buffer.add(
                obs=obs,
                action=action,
                log_prob=log_prob.item(),
                reward=reward,
                done=done,
                value=value.item()
            )
            
            # Update state
            obs = next_obs
            current_episode_reward += reward
            timestep += 1
            
            # Handle episode end
            if done:
                episode_rewards.append(current_episode_reward)
                current_episode_reward = 0
                episode_count += 1
                obs = env.reset()
            
            # Check if we've collected enough data
            if rollout_buffer.is_full():
                break
        
        # Compute next value for bootstrapping
        if not done:
            obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)
            _, _, next_value = model.act(obs_tensor)
            next_value = next_value.item()
        else:
            next_value = 0.0
        
        # Perform PPO update
        update_stats = trainer.update(
            rollout_buffer=rollout_buffer,
            n_epochs=config['n_epochs'],
            batch_size=config['batch_size'],
            gamma=config['gamma'],
            gae_lambda=config['gae_lambda'],
            next_value=next_value
        )
        
        # Clear buffer for next rollout
        rollout_buffer.clear()
        
        # Logging
        if timestep % config['log_interval'] == 0:
            avg_reward = np.mean(episode_rewards[-10:]) if episode_rewards else 0
            print(f"Step {timestep:6d} | Episodes: {episode_count:4d} | "
                  f"Avg Reward: {avg_reward:6.2f} | "
                  f"Policy Loss: {update_stats['policy_loss']:.4f} | "
                  f"Value Loss: {update_stats['value_loss']:.4f} | "
                  f"Recon Loss: {update_stats['recon_loss']:.4f}")
            
            # TensorBoard logging
            writer.add_scalar('Environment/Average_Reward', avg_reward, timestep)
            writer.add_scalar('Environment/Episodes', episode_count, timestep)
            for key, value in update_stats.items():
                writer.add_scalar(f'Training/{key}', value, timestep)
        
        # Save reconstruction images
        if timestep % config['eval_interval'] == 0:
            with torch.no_grad():
                # Get a batch of observations for reconstruction
                sample_obs = torch.tensor(
                    np.array([env.reset() for _ in range(4)]), 
                    dtype=torch.float32, device=device
                )
                recon_imgs = model.reconstruct(sample_obs)
                save_reconstruction_images(
                    sample_obs, recon_imgs, timestep, config['reconstruction_save_dir']
                )
        
        # Save model
        if timestep % config['save_interval'] == 0:
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': trainer.optimizer.state_dict(),
                'timestep': timestep,
                'config': config
            }, config['model_save_path'])
    
    # Final save
    torch.save({
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': trainer.optimizer.state_dict(),
        'timestep': timestep,
        'config': config
    }, config['model_save_path'])
    
    print(f"\nTraining completed! Model saved to {config['model_save_path']}")
    print(f"TensorBoard logs: {config['tensorboard_log']}")
    print(f"Reconstruction images: {config['reconstruction_save_dir']}")
    
    env.close()
    writer.close()


if __name__ == "__main__":
    main()
